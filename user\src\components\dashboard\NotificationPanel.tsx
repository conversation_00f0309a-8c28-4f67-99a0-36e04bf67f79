"use client";

import { X } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import NotificationItem from "./NotificationItem";

interface NotificationPanelProps {
  isOpen: boolean;
  onClose: () => void;
}

const notificationsData = [
  {
    time: "11:47 AM",
    action: "Buy BTC",
    amount: "0.0500 BTC",
    hasRedDot: true
  },
  {
    time: "07:42 AM", 
    action: "Buy MATIC",
    amount: "45,266.68 MATIC",
    hasRedDot: true
  },
  {
    time: "04:13 AM",
    action: "Sell BTC", 
    amount: "0.0500 BTC",
    hasRedDot: false
  },
  {
    time: "04:13 AM",
    action: "Sell BTC",
    amount: "0.0500 BTC", 
    hasRedDot: false
  }
];

export default function NotificationPanel({ isOpen, onClose }: NotificationPanelProps) {
  if (!isOpen) return null;

  return (
    <div className="absolute top-16 right-0 z-50 w-80">
      <Card className="shadow-lg border border-border">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-3">
          <h3 className="text-lg font-semibold">Notifications</h3>
          <Button 
            variant="ghost" 
            size="icon" 
            onClick={onClose}
            className="h-6 w-6 rounded-full"
          >
            <X className="h-4 w-4" />
          </Button>
        </CardHeader>
        <CardContent className="pt-0">
          <div className="space-y-3">
            <div className="text-sm font-medium text-muted-foreground">Today</div>
            <div className="space-y-1">
              {notificationsData.slice(0, 3).map((notification, index) => (
                <NotificationItem
                  key={index}
                  time={notification.time}
                  action={notification.action}
                  amount={notification.amount}
                  hasRedDot={notification.hasRedDot}
                />
              ))}
            </div>
            <div className="text-sm font-medium text-muted-foreground pt-2">April 24, 2023</div>
            <div className="space-y-1">
              {notificationsData.slice(3).map((notification, index) => (
                <NotificationItem
                  key={index + 3}
                  time={notification.time}
                  action={notification.action}
                  amount={notification.amount}
                  hasRedDot={notification.hasRedDot}
                />
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}