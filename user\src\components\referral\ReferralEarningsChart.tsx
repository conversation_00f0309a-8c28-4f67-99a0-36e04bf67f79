"use client";

import { Card } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { AreaChart, Area, XAxis, YAxis } from "recharts";

interface ReferralEarningData {
  month: string;
  earnings: number;
}

interface ReferralEarningsChartProps {
  data: ReferralEarningData[];
  chartConfig: any;
}

export function ReferralEarningsChart({ data, chartConfig }: ReferralEarningsChartProps) {
  return (
    <Card className="p-6 shadow-sm">
      <h2 className="text-lg font-semibold mb-6 text-gray-900">Referral earning metrics</h2>
      <div className="h-48">
        <ChartContainer config={chartConfig} className="w-full h-full">
          <AreaChart data={data}>
            <XAxis dataKey="month" axisLine={false} tickLine={false} />
            <YAxis axisLine={false} tickLine={false} />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Area 
              dataKey="earnings" 
              stroke="#3b82f6" 
              fill="#3b82f6" 
              fillOpacity={0.3}
              strokeWidth={2}
            />
          </AreaChart>
        </ChartContainer>
      </div>
    </Card>
  );
}