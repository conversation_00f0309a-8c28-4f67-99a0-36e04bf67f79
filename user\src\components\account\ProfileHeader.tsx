"use client";

import { Card } from "@/components/ui/card";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { ArrowLeft } from "lucide-react";
import { useState } from "react";
import { UserProfile } from "@/types/accountSchema";

interface ProfileHeaderProps {
  user: UserProfile;
  onNicknameChange?: (nickname: string) => void;
}

export function ProfileHeader({ user, onNicknameChange }: ProfileHeaderProps) {
  const [nickname, setNickname] = useState(user.nickname);

  const handleNicknameChange = (value: string) => {
    setNickname(value);
    onNicknameChange?.(value);
  };

  return (
    <div>
      <div className="relative shadow-2xl rounded-b-[5rem]"> {/* Apply shadow to wrapper div */}
        <Card className="absolute z-5 bottom-0 rounded-b-full left-1/2 transform -translate-x-1/2 translate-y-1/2 bg-gray-400/50 min-h-[3rem] min-w-[calc(100%-6.5rem)]">
        </Card>
        <Card className="relative z-10 bg-blue-600 text-white border-0 rounded-t-none rounded-b-[5rem] overflow-visible">
          <div className="p-6 relative pb-16">
            {/* Header with back arrow and logo */}
            <div className="flex items-center justify-between mb-8">
              <button className="text-white hover:text-blue-100 transition-colors">
                <ArrowLeft size={24} />
              </button>
              <div className="flex items-center gap-2">
                <div className="text-lg font-bold">LENDBLOC</div>
              </div>
            </div>

            {/* Avatar positioned at bottom center, hanging off by 50% */}
            <Avatar className="absolute z-50 bottom-5 left-1/2 transform -translate-x-1/2 translate-y-1/2 w-40 h-40 border-4 border-white/20 z-10"> {/* Changed w-40 h-40 to w-20 h-20 */}
              <AvatarImage src={user.avatar} alt={user.name} />
              <AvatarFallback className="bg-blue-500 text-white text-xl">
                {user.name.split(' ').map(n => n[0]).join('')}
              </AvatarFallback>
            </Avatar>
          </div>
        </Card>
      </div>
      <div className="flex mt-12 justify-center items-center">
        <div>
          <h1 className="text-3xl font-semibold text-center text-blue-600">John Doe</h1>
        </div>
      </div>
    </div>
  );
}