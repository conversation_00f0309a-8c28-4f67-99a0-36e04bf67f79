"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { CryptoCurrency, LoanHealth } from '@/types/enums';
import { getStatusStyles, getStatusFromLoanHealth } from '@/utils/statusStyles';
import { formatCryptoAmount, formatUSDAmount } from '@/utils/formatters';
import CryptoIcon from '../general/CryptoIcon';

interface LoanData {
  id: string;
  collateralAmount: number;
  collateralCurrency: CryptoCurrency;
  collateralValueUSD: number;
  currentRate: number;
  marginCall: number;
  ratePair: string;
  health?: LoanHealth;
  isHighlighted?: boolean;
}

interface LoanCardProps {
  loan: LoanData;
}

export const LoanCard: React.FC<LoanCardProps> = ({ loan }) => {
  const {
    id,
    collateralAmount,
    collateralCurrency,
    collateralValueUSD,
    currentRate,
    marginCall,
    ratePair,
    health,
    isHighlighted = false
  } = loan;
  // Ensure health is properly defined with fallback
  const safeHealth = health || LoanHealth.YELLOW;
  const statusVariant = getStatusFromLoanHealth(safeHealth);
  const statusStyles = getStatusStyles(statusVariant);

  // Add safety checks for numeric values
  const safeCollateralAmount = typeof collateralAmount === 'number' && !isNaN(collateralAmount) ? collateralAmount : 0;
  const safeCollateralValueUSD = typeof collateralValueUSD === 'number' && !isNaN(collateralValueUSD) ? collateralValueUSD : 0;
  const safeCurrentRate = typeof currentRate === 'number' && !isNaN(currentRate) ? currentRate : 0;
  const safeMarginCall = typeof marginCall === 'number' && !isNaN(marginCall) ? marginCall : 0;

  return (
    <Card
      className={`relative overflow-hidden rounded-2xl border-2 shadow-sm transition-all duration-200 hover:shadow-lg ${
        isHighlighted ? 'ring-2 ring-red-200' : ''
      }`}
      style={{
        backgroundColor: isHighlighted ? '#FFF2F2' : statusStyles.cardBG,
        borderColor: isHighlighted ? '#FF5353' : statusStyles.cardBorderColour,
      }}
    >
      <CardContent className="p-4">
        <div className="grid grid-cols-3 gap-4 items-center">
          {/* Collateral Column */}
          <div className="flex items-center gap-3">
            <CryptoIcon
              symbol={collateralCurrency}
              size={32}
              className="flex-shrink-0"
            />
            <div className="flex flex-col min-w-0">
              <p className="text-lg font-semibold truncate">
                {formatCryptoAmount(safeCollateralAmount, collateralCurrency)}
              </p>
              <p className="text-sm text-muted-foreground truncate">
                {safeCollateralValueUSD.toFixed(0)} USDT
              </p>
            </div>
          </div>

          {/* Current Rate Column */}
          <div className="text-center">
            <p className="text-lg font-semibold">
              {formatUSDAmount(safeCurrentRate)}
            </p>
            <p className="text-sm text-muted-foreground">
              {ratePair}
            </p>
          </div>

          {/* Margin Call Column */}
          <div className="flex items-center justify-between">
            <div className="text-right flex-1">
              <p className="text-lg font-semibold">
                {formatUSDAmount(safeMarginCall)}
              </p>
              <p className="text-sm text-muted-foreground">
                {ratePair}
              </p>
            </div>
            <div
              className="flex items-center justify-center rounded-full p-1 border ml-3"
              style={{ borderColor: statusStyles.statusBorderColour }}
            >
              <div
                className="w-3 h-3 rounded-full"
                style={{ backgroundColor: statusStyles.statusColour }}
              />
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};