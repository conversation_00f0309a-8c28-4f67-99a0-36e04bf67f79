"use client";

import React from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { CryptoCurrency, LoanHealth } from '@/types/enums';
import { getStatusStyles, getStatusFromLoanHealth } from '@/utils/statusStyles';
import { formatCryptoAmount, formatUSDAmount } from '@/utils/formatters';
import CryptoIcon from '../general/CryptoIcon';

interface LoanCardProps {
  id: string;
  collateralAmount: number;
  collateralCurrency: CryptoCurrency;
  collateralValueUSD: number;
  currentRate: number;
  marginCall: number;
  ratePair: string;
  health?: LoanHealth;
  isHighlighted?: boolean;
}

export const LoanCard: React.FC<LoanCardProps> = ({ 
  id,
  collateralAmount,
  collateralCurrency,
  collateralValueUSD,
  currentRate,
  marginCall,
  ratePair,
  health,
  isHighlighted = false
}) => {
  // Ensure health is properly defined with fallback
  const safeHealth = health || LoanHealth.YELLOW;
  const statusVariant = getStatusFromLoanHealth(safeHealth);
  const statusStyles = getStatusStyles(statusVariant);

  // Add safety checks for numeric values
  const safeCollateralAmount = typeof collateralAmount === 'number' && !isNaN(collateralAmount) ? collateralAmount : 0;
  const safeCollateralValueUSD = typeof collateralValueUSD === 'number' && !isNaN(collateralValueUSD) ? collateralValueUSD : 0;
  const safeCurrentRate = typeof currentRate === 'number' && !isNaN(currentRate) ? currentRate : 0;
  const safeMarginCall = typeof marginCall === 'number' && !isNaN(marginCall) ? marginCall : 0;

  return (
    <Card
      className={`relative overflow-hidden rounded-4xl border-2 shadow-sm h-full min-h-0 p-3 transition-all duration-200 hover:shadow-lg ${
        isHighlighted ? 'ring-2 ring-red-200' : ''
      }`}
      style={{
        backgroundColor: isHighlighted ? '#FFF2F2' : statusStyles.cardBG,
        borderColor: isHighlighted ? '#FF5353' : statusStyles.cardBorderColour,
      }}
    >
      <CardContent className="h-full flex flex-col justify-between">
        <div className="flex items-start justify-between py-3">
          <div className="flex items-center gap-2 md:gap-3">
            <CryptoIcon 
              symbol={collateralCurrency}
              size={28}
              className="p-2"
            />
            <div className="flex flex-col">
              <p className="sm:text-xs md:text-base lg:text-xl xl:text-2xl font-semibold">
                {formatCryptoAmount(safeCollateralAmount, collateralCurrency)}
              </p>
              <p className="text-xs text-muted-foreground">
                ${safeCollateralValueUSD.toFixed(2)} USDT
              </p>
            </div>
          </div>
          <div
            className="flex items-center justify-center rounded-full p-1 border"
            style={{ borderColor: statusStyles.statusBorderColour }}
          >
            <div
              className="w-2 h-2 md:w-3 md:h-3 rounded-full"
              style={{ backgroundColor: statusStyles.statusColour }}
            />
          </div>
        </div>

        <div className="mb-3 mt-3 md:mt-4 space-y-4">
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <p className="text-xs text-muted-foreground mb-1">Current rate</p>
              <p className="sm:text-xs md:text-sm lg:text-base xl:text-lg font-semibold tracking-tight">
                {formatUSDAmount(safeCurrentRate)}
              </p>
              <p className="text-xs text-muted-foreground">
                {ratePair}
              </p>
            </div>
            
            <div>
              <p className="text-xs text-muted-foreground mb-1">Margin call</p>
              <p className="sm:text-xs md:text-sm lg:text-base xl:text-lg font-semibold tracking-tight">
                {formatUSDAmount(safeMarginCall)}
              </p>
              <p className="text-xs text-muted-foreground">
                {ratePair}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};