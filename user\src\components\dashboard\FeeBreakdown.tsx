"use client";

import { ArrowRight, Info } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function FeeBreakdown() {
  return (
    <div className="mb-6">
      <h3 className="text-xl font-semibold text-blue-600 mb-4">Fee breakdown:</h3>
      <Card className="w-full rounded-3xl p-0 bg-[#F8F8F8] hover:bg-muted/50 transition-colors cursor-pointer">
        <Button 
          variant="ghost" 
          className="flex items-center justify-between w-full text-left p-4 h-auto hover:text-[#466DFF]"
        >
          <div className="flex items-center gap-2">
            <Info className="h-4 w-4" />
            <span>See our fee breakdown with full transparency</span>
          </div>
          <ArrowRight className="h-4 w-4 text-[#466DFF]" />
        </Button>
      </Card>
    </div>
  );
}