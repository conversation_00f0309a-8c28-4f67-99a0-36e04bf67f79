import { CryptoCurrency } from '../types/enums';

export const formatCryptoAmount = (amount: number, currency: CryptoCurrency): string => {
  return `${amount.toFixed(4)} ${currency}`;
};

export const formatUSDAmount = (amount: number): string => {
  return `${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

export const formatCryptoPair = (baseCurrency: CryptoCurrency, quoteCurrency: CryptoCurrency): string => {
  return `${baseCurrency}/${quoteCurrency}`;
};

export const formatPortfolioValue = (value: number): string => {
  return `$${value.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

export const formatPercentageChange = (change: number): string => {
  const sign = change >= 0 ? '+' : '';
  return `${sign}${change.toFixed(2)}%`;
};

export const formatSavingsAmount = (amount: number): string => {
  return `$${amount.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};

export const formatCryptoSavings = (amount: number, currency: string): string => {
  return `${amount.toFixed(8)} ${currency}`;
};

export const formatRank = (rank: number): string => {
  return `#${rank}`;
};

export const formatVolume = (volume: number): string => {
  return `$${volume.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
};