import { SavingsCryptoCurrency, OnboardingSlide } from '../types/enums';
// Data passed as props to the root component
export const mockRootProps = {
  onboardingSlides: [
    {
      id: OnboardingSlide.INTRO as const,
      title: "What is it and How it Works?",
      description: "Crypto savings let you earn extra digital money by depositing your cryptocurrency into a special online account. The platform then uses your crypto for things like lending or \"staking,\" sharing a portion of the profits back with you as rewards.",
      imageUrl: "https://images.unsplash.com/photo-*************-56dfc9eb5db4?crop=entropy&cs=srgb&fm=jpg&ixid=******************************************************************************************************************************&ixlib=rb-4.1.0&q=85",
      buttonText: "Next"
    },
    {
      id: OnboardingSlide.BENEFITS as const,
      title: "Why Consider It?",
      description: "The main draw of crypto savings is the potential for much higher returns compared to traditional bank accounts. You can earn significant passive income on your crypto holdings, allowing them to grow faster than with standard savings options.",
      imageUrl: "https://images.unsplash.com/photo-*************-025b90ba9f23?crop=entropy&cs=srgb&fm=jpg&ixid=***********************************************************************************************************************************************&ixlib=rb-4.1.0&q=85",
      buttonText: "Next"
    },
    {
      id: OnboardingSlide.POTENTIAL as const,
      title: "Maximize Your Crypto's Potential",
      description: "By carefully selecting reputable platforms and understanding the market's dynamics, you can navigate the inherent volatility and potentially unlock substantial growth that far outpaces traditional options. Think of it as a strategic way to grow your digital wealth, turning your idle crypto into a source of continuous income.",
      imageUrl: "https://images.unsplash.com/photo-*************-cfdda6f9821b?crop=entropy&cs=srgb&fm=jpg&ixid=M3w3NTAwNDR8MHwxfHNlYXJjaHw3fHxmaW5hbmNpYWwlMjBncm93dGglMjBjb2lucyUyMHVwd2FyZCUyMGFycm93JTIwc2lsaG91ZXR0ZXxlbnwwfDB8fG9yYW5nZXwxNzU0NTI1MjAzfDA&ixlib=rb-4.1.0&q=85",
      buttonText: "Done"
    }
  ],
  savingsData: {
    totalSavings: 8123.00,
    totalReward: 98.00,
    holdings: [
      {
        currency: SavingsCryptoCurrency.BTC as const,
        name: "Bitcoin",
        savedAmount: 0.02500000,
        symbol: "BTC"
      },
      {
        currency: SavingsCryptoCurrency.ETH as const,
        name: "Ethereum", 
        savedAmount: 0.02500000,
        symbol: "ETH"
      },
      {
        currency: SavingsCryptoCurrency.BNB as const,
        name: "BNB",
        savedAmount: 0.02500000,
        symbol: "BNB"
      },
      {
        currency: SavingsCryptoCurrency.SOL as const,
        name: "Solana",
        savedAmount: 0.02500000,
        symbol: "SOL"
      },
      {
        currency: SavingsCryptoCurrency.DASH as const,
        name: "DASH",
        savedAmount: 0.02500000,
        symbol: "DASH"
      }
    ]
  },
  topTrends: [
    {
      rank: 1,
      asset: SavingsCryptoCurrency.BTC as const,
      collateralValue: 80125.00,
      volume24h: 4589.00
    },
    {
      rank: 2,
      asset: SavingsCryptoCurrency.ETH as const,
      collateralValue: 80125.00,
      volume24h: 4589.00
    },
    {
      rank: 3,
      asset: SavingsCryptoCurrency.SOL as const,
      collateralValue: 80125.00,
      volume24h: 4589.00
    },
    {
      rank: 4,
      asset: SavingsCryptoCurrency.MONERO as const,
      collateralValue: 80125.00,
      volume24h: 4589.00
    },
    {
      rank: 5,
      asset: SavingsCryptoCurrency.POLKADOT as const,
      collateralValue: 80125.00,
      volume24h: 4589.00
    }
  ]
};