"use client";

import { Card } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Bar<PERSON><PERSON>, Bar, XAxis, YAxis } from "recharts";
import { PeriodFilter } from "./PeriodFilter";

interface ReferralPerformanceData {
  month: string;
  totalReferrals: number;
  activeLoans: number;
}

interface ReferralPerformanceChartProps {
  data: ReferralPerformanceData[];
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
  chartConfig: any;
}

export function ReferralPerformanceChart({ 
  data, 
  selectedPeriod, 
  onPeriodChange, 
  chartConfig 
}: ReferralPerformanceChartProps) {
  return (
    <Card className="p-6 flex-1 shadow-sm">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Referral performance metrics</h2>
        <PeriodFilter 
          selectedPeriod={selectedPeriod}
          onPeriodChange={onPeriodChange}
          periods={["All time", "Yearly"]}
        />
      </div>
      <div className="h-64">
        <ChartContainer config={chartConfig} className="w-full h-full">
          <BarChart data={data}>
            <XAxis dataKey="month" axisLine={false} tickLine={false} />
            <YAxis axisLine={false} tickLine={false} />
            <ChartTooltip content={<ChartTooltipContent />} />
            <Bar dataKey="totalReferrals" fill="#22c55e" radius={[2, 2, 0, 0]} barSize={5} />
            <Bar dataKey="activeLoans" fill="#3b82f6" radius={[2, 2, 0, 0]} barSize={5} />
          </BarChart>
        </ChartContainer>
      </div>
      <div className="flex items-center justify-end gap-6 mt-4">
        <div className="flex items-center gap-2">
          <div className="w-16 h-2 bg-green-500 rounded-sm"></div>
          <span className="text-sm text-gray-600">Total referrals</span>
        </div>
        <div className="flex items-center gap-2">
          <div className="w-16 h-2 bg-blue-500 rounded-sm"></div>
          <span className="text-sm text-gray-600">Active loans</span>
        </div>
      </div>
    </Card>
  );
}