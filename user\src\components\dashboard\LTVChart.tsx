"use client";

import { Info } from "lucide-react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { Line, LineChart, XAxis, <PERSON>Axis, CartesianGrid, ReferenceLine, Dot } from "recharts";
import { LTVChartDataPoint } from "@/data/dashboard-data";

interface LTVChartProps {
  data: LTVChartDataPoint[];
}

const chartConfig = {
  ltvRatio: {
    label: "LTV Ratio (%)",
    color: "#6366f1",
  }
};

export default function LTVChart({ data }: LTVChartProps) {
  return (
    <Card className="mb-6">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4">
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          LTV Ratio vs Collateral Value
          <Info className="h-4 w-4 text-muted-foreground" />
        </CardTitle>
        <div className="flex items-center gap-4 text-sm">
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-red-500"></div>
            <span className="text-muted-foreground">Liquidation trigger</span>
          </div>
          <div className="flex items-center gap-2">
            <div className="w-3 h-3 rounded-full bg-green-500"></div>
            <span className="text-muted-foreground">Valid position</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[300px] w-full">
          <ChartContainer config={chartConfig} className="h-full w-full">
            <LineChart data={data} margin={{ top: 20, right: 30, left: 20, bottom: 20 }}>
              <CartesianGrid strokeDasharray="3 3" stroke="#e5e7eb" />
              <XAxis 
                dataKey="collateralValue" 
                tickFormatter={(value) => `$${value}`}
                stroke="#6b7280"
              />
              <YAxis 
                tickFormatter={(value) => `${value}%`}
                stroke="#6b7280"
              />
              <ChartTooltip 
                content={<ChartTooltipContent />}
                formatter={(value, name) => [`${value}%`, "LTV Ratio"]}
                labelFormatter={(value) => `Collateral Value: $${value}`}
              />
              <ReferenceLine y={30} stroke="#ef4444" strokeDasharray="5 5" />
              <ReferenceLine y={15} stroke="#22c55e" strokeDasharray="5 5" />
              <Line 
                type="monotone" 
                dataKey="ltvRatio" 
                stroke={chartConfig.ltvRatio.color}
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 6, fill: chartConfig.ltvRatio.color }}
              />
              <Dot cx={400} cy={150} r={4} fill="#ef4444" />
              <Dot cx={600} cy={200} r={4} fill="#22c55e" />
            </LineChart>
          </ChartContainer>
        </div>
      </CardContent>
    </Card>
  );
}