"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/loan/page",{

/***/ "(app-pages-browser)/./src/app/loan/page.tsx":
/*!*******************************!*\
  !*** ./src/app/loan/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LoanPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/use-viewport */ \"(app-pages-browser)/./src/hooks/use-viewport.ts\");\n/* harmony import */ var _components_loan_LoanFilters__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/loan/LoanFilters */ \"(app-pages-browser)/./src/components/loan/LoanFilters.tsx\");\n/* harmony import */ var _components_loan_LoanCard__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/loan/LoanCard */ \"(app-pages-browser)/./src/components/loan/LoanCard.tsx\");\n/* harmony import */ var _components_loan_PortfolioInsights__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/loan/PortfolioInsights */ \"(app-pages-browser)/./src/components/loan/PortfolioInsights.tsx\");\n/* harmony import */ var _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/data/loanDashboardMockData */ \"(app-pages-browser)/./src/data/loanDashboardMockData.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction LoanPage() {\n    _s();\n    const { isMobile, isTablet } = (0,_hooks_use_viewport__WEBPACK_IMPORTED_MODULE_2__.useViewportType)();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [healthFilter, setHealthFilter] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    // Filter loans based on search term and health status\n    const filteredLoans = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)({\n        \"LoanPage.useMemo[filteredLoans]\": ()=>{\n            return _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_6__.mockRootProps.loans.filter({\n                \"LoanPage.useMemo[filteredLoans]\": (loan)=>{\n                    const matchesSearch = loan.collateralCurrency.toLowerCase().includes(searchTerm.toLowerCase()) || loan.ratePair.toLowerCase().includes(searchTerm.toLowerCase());\n                    const matchesHealth = healthFilter === 'all' || loan.health === healthFilter;\n                    return matchesSearch && matchesHealth;\n                }\n            }[\"LoanPage.useMemo[filteredLoans]\"]);\n        }\n    }[\"LoanPage.useMemo[filteredLoans]\"], [\n        searchTerm,\n        healthFilter\n    ]);\n    const handleGetLoan = ()=>{\n        console.log('Get loan clicked');\n    };\n    const handleDownloadCSV = ()=>{\n        console.log('Download CSV clicked');\n    };\n    // Mobile and Tablet Layout (< 1200px)\n    if (isMobile || isTablet) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-screen p-2 sm:p-4 bg-gray-50 overflow-hidden\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full flex flex-col gap-2 sm:gap-4 overflow-hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loan_LoanFilters__WEBPACK_IMPORTED_MODULE_3__.LoanFilters, {\n                            searchTerm: searchTerm,\n                            onSearchChange: setSearchTerm,\n                            healthFilter: healthFilter,\n                            onHealthFilterChange: setHealthFilter,\n                            onGetLoan: handleGetLoan,\n                            onDownloadCSV: handleDownloadCSV\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                            lineNumber: 40,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                        lineNumber: 39,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-1 min-h-0 max-h-full overflow-y-auto space-y-2 sm:space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-3 gap-4 px-4 py-2 text-sm font-medium text-muted-foreground\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: \"Collateral\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center\",\n                                        children: \"Current rate\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                                        lineNumber: 54,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-right\",\n                                        children: \"Margin call\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                                        lineNumber: 55,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                                lineNumber: 52,\n                                columnNumber: 13\n                            }, this),\n                            filteredLoans.map((loan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loan_LoanCard__WEBPACK_IMPORTED_MODULE_4__.LoanCard, {\n                                    loan: loan\n                                }, loan.id, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                                    lineNumber: 59,\n                                    columnNumber: 15\n                                }, this))\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                        lineNumber: 50,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loan_PortfolioInsights__WEBPACK_IMPORTED_MODULE_5__.PortfolioInsights, {\n                            portfolioInsights: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_6__.mockRootProps.portfolioInsights\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                            lineNumber: 64,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                        lineNumber: 63,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                lineNumber: 38,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n            lineNumber: 37,\n            columnNumber: 7\n        }, this);\n    }\n    // Desktop Layout (≥ 1200px)\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"w-full h-screen p-4 lg:p-6 bg-gray-300/50 overflow-hidden\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"w-full h-full grid grid-cols-1 gap-4 lg:gap-6 xl:grid-cols-[6fr_4fr] overflow-hidden\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 max-h-full flex flex-col gap-4 lg:gap-6 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-shrink-0\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loan_LoanFilters__WEBPACK_IMPORTED_MODULE_3__.LoanFilters, {\n                                searchTerm: searchTerm,\n                                onSearchChange: setSearchTerm,\n                                healthFilter: healthFilter,\n                                onHealthFilterChange: setHealthFilter,\n                                onGetLoan: handleGetLoan,\n                                onDownloadCSV: handleDownloadCSV\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 min-h-0 max-h-full overflow-y-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 gap-3 lg:gap-4\",\n                                children: filteredLoans.map((loan)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loan_LoanCard__WEBPACK_IMPORTED_MODULE_4__.LoanCard, {\n                                        loan: loan\n                                    }, loan.id, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                                lineNumber: 88,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                    lineNumber: 75,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"min-h-0 max-h-full overflow-hidden\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loan_PortfolioInsights__WEBPACK_IMPORTED_MODULE_5__.PortfolioInsights, {\n                        portfolioInsights: _data_loanDashboardMockData__WEBPACK_IMPORTED_MODULE_6__.mockRootProps.portfolioInsights\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n            lineNumber: 74,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\app\\\\loan\\\\page.tsx\",\n        lineNumber: 73,\n        columnNumber: 5\n    }, this);\n}\n_s(LoanPage, \"S49gLhJ2xsTVJTIW6rTK6l7yQM8=\", false, function() {\n    return [\n        _hooks_use_viewport__WEBPACK_IMPORTED_MODULE_2__.useViewportType\n    ];\n});\n_c = LoanPage;\nvar _c;\n$RefreshReg$(_c, \"LoanPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvbG9hbi9wYWdlLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7OztBQUVpRDtBQUNNO0FBQ0s7QUFDTjtBQUNrQjtBQUNYO0FBRzlDLFNBQVNROztJQUN0QixNQUFNLEVBQUVDLFFBQVEsRUFBRUMsUUFBUSxFQUFFLEdBQUdQLG9FQUFlQTtJQUM5QyxNQUFNLENBQUNRLFlBQVlDLGNBQWMsR0FBR1gsK0NBQVFBLENBQUM7SUFDN0MsTUFBTSxDQUFDWSxjQUFjQyxnQkFBZ0IsR0FBR2IsK0NBQVFBLENBQUM7SUFFakQsc0RBQXNEO0lBQ3RELE1BQU1jLGdCQUFnQmIsOENBQU9BOzJDQUFDO1lBQzVCLE9BQU9LLHNFQUFhQSxDQUFDUyxLQUFLLENBQUNDLE1BQU07bURBQUNDLENBQUFBO29CQUNoQyxNQUFNQyxnQkFBZ0JELEtBQUtFLGtCQUFrQixDQUFDQyxXQUFXLEdBQUdDLFFBQVEsQ0FBQ1gsV0FBV1UsV0FBVyxPQUN0RUgsS0FBS0ssUUFBUSxDQUFDRixXQUFXLEdBQUdDLFFBQVEsQ0FBQ1gsV0FBV1UsV0FBVztvQkFDaEYsTUFBTUcsZ0JBQWdCWCxpQkFBaUIsU0FBU0ssS0FBS08sTUFBTSxLQUFLWjtvQkFDaEUsT0FBT00saUJBQWlCSztnQkFDMUI7O1FBQ0Y7MENBQUc7UUFBQ2I7UUFBWUU7S0FBYTtJQUU3QixNQUFNYSxnQkFBZ0I7UUFDcEJDLFFBQVFDLEdBQUcsQ0FBQztJQUNkO0lBRUEsTUFBTUMsb0JBQW9CO1FBQ3hCRixRQUFRQyxHQUFHLENBQUM7SUFDZDtJQUVBLHNDQUFzQztJQUN0QyxJQUFJbkIsWUFBWUMsVUFBVTtRQUN4QixxQkFDRSw4REFBQ29CO1lBQUlDLFdBQVU7c0JBQ2IsNEVBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0Q7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUMzQixxRUFBV0E7NEJBQ1ZPLFlBQVlBOzRCQUNacUIsZ0JBQWdCcEI7NEJBQ2hCQyxjQUFjQTs0QkFDZG9CLHNCQUFzQm5COzRCQUN0Qm9CLFdBQVdSOzRCQUNYUyxlQUFlTjs7Ozs7Ozs7Ozs7a0NBSW5CLDhEQUFDQzt3QkFBSUMsV0FBVTs7MENBRWIsOERBQUNEO2dDQUFJQyxXQUFVOztrREFDYiw4REFBQ0Q7a0RBQUk7Ozs7OztrREFDTCw4REFBQ0E7d0NBQUlDLFdBQVU7a0RBQWM7Ozs7OztrREFDN0IsOERBQUNEO3dDQUFJQyxXQUFVO2tEQUFhOzs7Ozs7Ozs7Ozs7NEJBRzdCaEIsY0FBY3FCLEdBQUcsQ0FBQyxDQUFDbEIscUJBQ2xCLDhEQUFDYiwrREFBUUE7b0NBQWVhLE1BQU1BO21DQUFmQSxLQUFLbUIsRUFBRTs7Ozs7Ozs7Ozs7a0NBSTFCLDhEQUFDUDt3QkFBSUMsV0FBVTtrQ0FDYiw0RUFBQ3pCLGlGQUFpQkE7NEJBQUNnQyxtQkFBbUIvQixzRUFBYUEsQ0FBQytCLGlCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztJQUsvRTtJQUVBLDRCQUE0QjtJQUM1QixxQkFDRSw4REFBQ1I7UUFBSUMsV0FBVTtrQkFDYiw0RUFBQ0Q7WUFBSUMsV0FBVTs7OEJBQ2IsOERBQUNEO29CQUFJQyxXQUFVOztzQ0FDYiw4REFBQ0Q7NEJBQUlDLFdBQVU7c0NBQ2IsNEVBQUMzQixxRUFBV0E7Z0NBQ1ZPLFlBQVlBO2dDQUNacUIsZ0JBQWdCcEI7Z0NBQ2hCQyxjQUFjQTtnQ0FDZG9CLHNCQUFzQm5CO2dDQUN0Qm9CLFdBQVdSO2dDQUNYUyxlQUFlTjs7Ozs7Ozs7Ozs7c0NBSW5CLDhEQUFDQzs0QkFBSUMsV0FBVTtzQ0FDYiw0RUFBQ0Q7Z0NBQUlDLFdBQVU7MENBQ1poQixjQUFjcUIsR0FBRyxDQUFDLENBQUNsQixxQkFDbEIsOERBQUNiLCtEQUFRQTt3Q0FBZWEsTUFBTUE7dUNBQWZBLEtBQUttQixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OEJBTTlCLDhEQUFDUDtvQkFBSUMsV0FBVTs4QkFDYiw0RUFBQ3pCLGlGQUFpQkE7d0JBQUNnQyxtQkFBbUIvQixzRUFBYUEsQ0FBQytCLGlCQUFpQjs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUsvRTtHQTNGd0I5Qjs7UUFDU0wsZ0VBQWVBOzs7S0FEeEJLIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXHNvbG9tXFxEb2N1bWVudHNcXDAwMVdvcmtQcm9qZWN0XFxsZW5kYmxvY1xcdXNlclxcc3JjXFxhcHBcXGxvYW5cXHBhZ2UudHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiO1xuXG5pbXBvcnQgUmVhY3QsIHsgdXNlU3RhdGUsIHVzZU1lbW8gfSBmcm9tICdyZWFjdCc7XG5pbXBvcnQgeyB1c2VWaWV3cG9ydFR5cGUgfSBmcm9tIFwiQC9ob29rcy91c2Utdmlld3BvcnRcIjtcbmltcG9ydCB7IExvYW5GaWx0ZXJzIH0gZnJvbSAnQC9jb21wb25lbnRzL2xvYW4vTG9hbkZpbHRlcnMnO1xuaW1wb3J0IHsgTG9hbkNhcmQgfSBmcm9tICdAL2NvbXBvbmVudHMvbG9hbi9Mb2FuQ2FyZCc7XG5pbXBvcnQgeyBQb3J0Zm9saW9JbnNpZ2h0cyB9IGZyb20gJ0AvY29tcG9uZW50cy9sb2FuL1BvcnRmb2xpb0luc2lnaHRzJztcbmltcG9ydCB7IG1vY2tSb290UHJvcHMgfSBmcm9tICdAL2RhdGEvbG9hbkRhc2hib2FyZE1vY2tEYXRhJztcbmltcG9ydCB7IExvYW5IZWFsdGggfSBmcm9tICdAL3R5cGVzL2VudW1zJztcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTG9hblBhZ2UoKSB7XG4gIGNvbnN0IHsgaXNNb2JpbGUsIGlzVGFibGV0IH0gPSB1c2VWaWV3cG9ydFR5cGUoKTtcbiAgY29uc3QgW3NlYXJjaFRlcm0sIHNldFNlYXJjaFRlcm1dID0gdXNlU3RhdGUoJycpO1xuICBjb25zdCBbaGVhbHRoRmlsdGVyLCBzZXRIZWFsdGhGaWx0ZXJdID0gdXNlU3RhdGUoJ2FsbCcpO1xuXG4gIC8vIEZpbHRlciBsb2FucyBiYXNlZCBvbiBzZWFyY2ggdGVybSBhbmQgaGVhbHRoIHN0YXR1c1xuICBjb25zdCBmaWx0ZXJlZExvYW5zID0gdXNlTWVtbygoKSA9PiB7XG4gICAgcmV0dXJuIG1vY2tSb290UHJvcHMubG9hbnMuZmlsdGVyKGxvYW4gPT4ge1xuICAgICAgY29uc3QgbWF0Y2hlc1NlYXJjaCA9IGxvYW4uY29sbGF0ZXJhbEN1cnJlbmN5LnRvTG93ZXJDYXNlKCkuaW5jbHVkZXMoc2VhcmNoVGVybS50b0xvd2VyQ2FzZSgpKSB8fFxuICAgICAgICAgICAgICAgICAgICAgICAgICAgbG9hbi5yYXRlUGFpci50b0xvd2VyQ2FzZSgpLmluY2x1ZGVzKHNlYXJjaFRlcm0udG9Mb3dlckNhc2UoKSk7XG4gICAgICBjb25zdCBtYXRjaGVzSGVhbHRoID0gaGVhbHRoRmlsdGVyID09PSAnYWxsJyB8fCBsb2FuLmhlYWx0aCA9PT0gaGVhbHRoRmlsdGVyO1xuICAgICAgcmV0dXJuIG1hdGNoZXNTZWFyY2ggJiYgbWF0Y2hlc0hlYWx0aDtcbiAgICB9KTtcbiAgfSwgW3NlYXJjaFRlcm0sIGhlYWx0aEZpbHRlcl0pO1xuXG4gIGNvbnN0IGhhbmRsZUdldExvYW4gPSAoKSA9PiB7XG4gICAgY29uc29sZS5sb2coJ0dldCBsb2FuIGNsaWNrZWQnKTtcbiAgfTtcblxuICBjb25zdCBoYW5kbGVEb3dubG9hZENTViA9ICgpID0+IHtcbiAgICBjb25zb2xlLmxvZygnRG93bmxvYWQgQ1NWIGNsaWNrZWQnKTtcbiAgfTtcblxuICAvLyBNb2JpbGUgYW5kIFRhYmxldCBMYXlvdXQgKDwgMTIwMHB4KVxuICBpZiAoaXNNb2JpbGUgfHwgaXNUYWJsZXQpIHtcbiAgICByZXR1cm4gKFxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJ3LWZ1bGwgaC1zY3JlZW4gcC0yIHNtOnAtNCBiZy1ncmF5LTUwIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgZmxleCBmbGV4LWNvbCBnYXAtMiBzbTpnYXAtNCBvdmVyZmxvdy1oaWRkZW5cIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtc2hyaW5rLTBcIj5cbiAgICAgICAgICAgIDxMb2FuRmlsdGVyc1xuICAgICAgICAgICAgICBzZWFyY2hUZXJtPXtzZWFyY2hUZXJtfVxuICAgICAgICAgICAgICBvblNlYXJjaENoYW5nZT17c2V0U2VhcmNoVGVybX1cbiAgICAgICAgICAgICAgaGVhbHRoRmlsdGVyPXtoZWFsdGhGaWx0ZXJ9XG4gICAgICAgICAgICAgIG9uSGVhbHRoRmlsdGVyQ2hhbmdlPXtzZXRIZWFsdGhGaWx0ZXJ9XG4gICAgICAgICAgICAgIG9uR2V0TG9hbj17aGFuZGxlR2V0TG9hbn1cbiAgICAgICAgICAgICAgb25Eb3dubG9hZENTVj17aGFuZGxlRG93bmxvYWRDU1Z9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LTEgbWluLWgtMCBtYXgtaC1mdWxsIG92ZXJmbG93LXktYXV0byBzcGFjZS15LTIgc206c3BhY2UteS00XCI+XG4gICAgICAgICAgICB7LyogSGVhZGVyIFJvdyAqL31cbiAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZ3JpZCBncmlkLWNvbHMtMyBnYXAtNCBweC00IHB5LTIgdGV4dC1zbSBmb250LW1lZGl1bSB0ZXh0LW11dGVkLWZvcmVncm91bmRcIj5cbiAgICAgICAgICAgICAgPGRpdj5Db2xsYXRlcmFsPC9kaXY+XG4gICAgICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwidGV4dC1jZW50ZXJcIj5DdXJyZW50IHJhdGU8L2Rpdj5cbiAgICAgICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJ0ZXh0LXJpZ2h0XCI+TWFyZ2luIGNhbGw8L2Rpdj5cbiAgICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgICB7ZmlsdGVyZWRMb2Fucy5tYXAoKGxvYW4pID0+IChcbiAgICAgICAgICAgICAgPExvYW5DYXJkIGtleT17bG9hbi5pZH0gbG9hbj17bG9hbn0gLz5cbiAgICAgICAgICAgICkpfVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4LXNocmluay0wXCI+XG4gICAgICAgICAgICA8UG9ydGZvbGlvSW5zaWdodHMgcG9ydGZvbGlvSW5zaWdodHM9e21vY2tSb290UHJvcHMucG9ydGZvbGlvSW5zaWdodHN9IC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgKTtcbiAgfVxuXG4gIC8vIERlc2t0b3AgTGF5b3V0ICjiiaUgMTIwMHB4KVxuICByZXR1cm4gKFxuICAgIDxkaXYgY2xhc3NOYW1lPVwidy1mdWxsIGgtc2NyZWVuIHAtNCBsZzpwLTYgYmctZ3JheS0zMDAvNTAgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICA8ZGl2IGNsYXNzTmFtZT1cInctZnVsbCBoLWZ1bGwgZ3JpZCBncmlkLWNvbHMtMSBnYXAtNCBsZzpnYXAtNiB4bDpncmlkLWNvbHMtWzZmcl80ZnJdIG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1pbi1oLTAgbWF4LWgtZnVsbCBmbGV4IGZsZXgtY29sIGdhcC00IGxnOmdhcC02IG92ZXJmbG93LWhpZGRlblwiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleC1zaHJpbmstMFwiPlxuICAgICAgICAgICAgPExvYW5GaWx0ZXJzXG4gICAgICAgICAgICAgIHNlYXJjaFRlcm09e3NlYXJjaFRlcm19XG4gICAgICAgICAgICAgIG9uU2VhcmNoQ2hhbmdlPXtzZXRTZWFyY2hUZXJtfVxuICAgICAgICAgICAgICBoZWFsdGhGaWx0ZXI9e2hlYWx0aEZpbHRlcn1cbiAgICAgICAgICAgICAgb25IZWFsdGhGaWx0ZXJDaGFuZ2U9e3NldEhlYWx0aEZpbHRlcn1cbiAgICAgICAgICAgICAgb25HZXRMb2FuPXtoYW5kbGVHZXRMb2FufVxuICAgICAgICAgICAgICBvbkRvd25sb2FkQ1NWPXtoYW5kbGVEb3dubG9hZENTVn1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXgtMSBtaW4taC0wIG1heC1oLWZ1bGwgb3ZlcmZsb3cteS1hdXRvXCI+XG4gICAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImdyaWQgZ3JpZC1jb2xzLTEgZ2FwLTMgbGc6Z2FwLTRcIj5cbiAgICAgICAgICAgICAge2ZpbHRlcmVkTG9hbnMubWFwKChsb2FuKSA9PiAoXG4gICAgICAgICAgICAgICAgPExvYW5DYXJkIGtleT17bG9hbi5pZH0gbG9hbj17bG9hbn0gLz5cbiAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJtaW4taC0wIG1heC1oLWZ1bGwgb3ZlcmZsb3ctaGlkZGVuXCI+XG4gICAgICAgICAgPFBvcnRmb2xpb0luc2lnaHRzIHBvcnRmb2xpb0luc2lnaHRzPXttb2NrUm9vdFByb3BzLnBvcnRmb2xpb0luc2lnaHRzfSAvPlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuICAgIDwvZGl2PlxuICApO1xufSJdLCJuYW1lcyI6WyJSZWFjdCIsInVzZVN0YXRlIiwidXNlTWVtbyIsInVzZVZpZXdwb3J0VHlwZSIsIkxvYW5GaWx0ZXJzIiwiTG9hbkNhcmQiLCJQb3J0Zm9saW9JbnNpZ2h0cyIsIm1vY2tSb290UHJvcHMiLCJMb2FuUGFnZSIsImlzTW9iaWxlIiwiaXNUYWJsZXQiLCJzZWFyY2hUZXJtIiwic2V0U2VhcmNoVGVybSIsImhlYWx0aEZpbHRlciIsInNldEhlYWx0aEZpbHRlciIsImZpbHRlcmVkTG9hbnMiLCJsb2FucyIsImZpbHRlciIsImxvYW4iLCJtYXRjaGVzU2VhcmNoIiwiY29sbGF0ZXJhbEN1cnJlbmN5IiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsInJhdGVQYWlyIiwibWF0Y2hlc0hlYWx0aCIsImhlYWx0aCIsImhhbmRsZUdldExvYW4iLCJjb25zb2xlIiwibG9nIiwiaGFuZGxlRG93bmxvYWRDU1YiLCJkaXYiLCJjbGFzc05hbWUiLCJvblNlYXJjaENoYW5nZSIsIm9uSGVhbHRoRmlsdGVyQ2hhbmdlIiwib25HZXRMb2FuIiwib25Eb3dubG9hZENTViIsIm1hcCIsImlkIiwicG9ydGZvbGlvSW5zaWdodHMiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/loan/page.tsx\n"));

/***/ })

});