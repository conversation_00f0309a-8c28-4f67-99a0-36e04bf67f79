"use client";

import React from 'react';
import { Card } from '@/components/ui/card';
import { Bitcoin, Zap, ChevronRight } from 'lucide-react';
import { SavingsHolding as SavingsHoldingType } from '../../types/schema';
import { SavingsCryptoCurrency } from '../../types/enums';
import { formatCryptoSavings } from '../../utils/formatters';

interface SavingsHoldingProps {
  holding: SavingsHoldingType;
}

const getCryptocurrencyIcon = (currency: SavingsCryptoCurrency) => {
  switch (currency) {
    case SavingsCryptoCurrency.BTC:
      return <div className="w-12 h-12 rounded-full bg-orange-500 flex items-center justify-center">
        <Bitcoin className="w-7 h-7 text-white" />
      </div>;
    case SavingsCryptoCurrency.ETH:
      return <div className="w-12 h-12 rounded-full bg-gray-500 flex items-center justify-center">
        <Zap className="w-7 h-7 text-white" />
      </div>;
    case SavingsCryptoCurrency.BNB:
      return <div className="w-12 h-12 rounded-full bg-yellow-500 flex items-center justify-center text-white font-bold text-lg">
        B
      </div>;
    case SavingsCryptoCurrency.SOL:
      return <div className="w-12 h-12 rounded-full bg-purple-500 flex items-center justify-center text-white font-bold text-lg">
        S
      </div>;
    case SavingsCryptoCurrency.DASH:
      return <div className="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white font-bold text-lg">
        D
      </div>;
    default:
      return <div className="w-12 h-12 rounded-full bg-gray-400" />;
  }
};

export const SavingsHolding: React.FC<SavingsHoldingProps> = ({ holding }) => {
  return (
    <Card className="p-4 hover:bg-gray-50 transition-colors cursor-pointer">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          {getCryptocurrencyIcon(holding.currency)}
          <div>
            <div className="font-semibold text-lg">{holding.name}</div>
            <div className="text-sm text-muted-foreground">
              Saved: {formatCryptoSavings(holding.savedAmount, holding.symbol)}
            </div>
          </div>
        </div>
        
        <ChevronRight className="w-5 h-5 text-muted-foreground" />
      </div>
    </Card>
  );
};