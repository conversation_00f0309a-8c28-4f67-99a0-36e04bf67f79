// TypeScript schemas for account page data
export interface UserProfile {
  name: string;
  nickname: string;
  avatar: string;
}

export interface PrivacySettings {
  showAssetsValue: boolean;
  twoFactorAuth: boolean;
}

export interface AppSettings {
  displayMode: string;
  language: string;
}

export interface KYCStatus {
  nationalIdVerified: boolean;
  faceRecognitionApproved: boolean;
  addressVerified: boolean;
}

export interface AccountPageProps {
  user: UserProfile;
  privacySettings: PrivacySettings;
  appSettings: AppSettings;
  kycStatus: KYCStatus;
}