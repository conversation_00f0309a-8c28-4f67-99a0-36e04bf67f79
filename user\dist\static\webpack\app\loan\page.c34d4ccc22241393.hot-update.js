"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/loan/page",{

/***/ "(app-pages-browser)/./src/components/loan/LoanCard.tsx":
/*!******************************************!*\
  !*** ./src/components/loan/LoanCard.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoanCard: () => (/* binding */ LoanCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _types_enums__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/types/enums */ \"(app-pages-browser)/./src/types/enums.ts\");\n/* harmony import */ var _utils_statusStyles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/statusStyles */ \"(app-pages-browser)/./src/utils/statusStyles.ts\");\n/* harmony import */ var _utils_formatters__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/formatters */ \"(app-pages-browser)/./src/utils/formatters.ts\");\n/* harmony import */ var _general_CryptoIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../general/CryptoIcon */ \"(app-pages-browser)/./src/components/general/CryptoIcon.tsx\");\n/* __next_internal_client_entry_do_not_use__ LoanCard auto */ \n\n\n\n\n\n\nconst LoanCard = (param)=>{\n    let { loan } = param;\n    const { id, collateralAmount, collateralCurrency, collateralValueUSD, currentRate, marginCall, ratePair, health, isHighlighted = false } = loan;\n    // Ensure health is properly defined with fallback\n    const safeHealth = health || _types_enums__WEBPACK_IMPORTED_MODULE_3__.LoanHealth.YELLOW;\n    const statusVariant = (0,_utils_statusStyles__WEBPACK_IMPORTED_MODULE_4__.getStatusFromLoanHealth)(safeHealth);\n    const statusStyles = (0,_utils_statusStyles__WEBPACK_IMPORTED_MODULE_4__.getStatusStyles)(statusVariant);\n    // Add safety checks for numeric values\n    const safeCollateralAmount = typeof collateralAmount === 'number' && !isNaN(collateralAmount) ? collateralAmount : 0;\n    const safeCollateralValueUSD = typeof collateralValueUSD === 'number' && !isNaN(collateralValueUSD) ? collateralValueUSD : 0;\n    const safeCurrentRate = typeof currentRate === 'number' && !isNaN(currentRate) ? currentRate : 0;\n    const safeMarginCall = typeof marginCall === 'number' && !isNaN(marginCall) ? marginCall : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"relative overflow-hidden rounded-4xl border-2 shadow-sm h-full min-h-0 p-3 transition-all duration-200 hover:shadow-lg \".concat(isHighlighted ? 'ring-2 ring-red-200' : ''),\n        style: {\n            backgroundColor: isHighlighted ? '#FFF2F2' : statusStyles.cardBG,\n            borderColor: isHighlighted ? '#FF5353' : statusStyles.cardBorderColour\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n            className: \"h-full flex flex-col justify-between\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-start justify-between py-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-2 md:gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_general_CryptoIcon__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                    symbol: collateralCurrency,\n                                    size: 28,\n                                    className: \"p-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                    lineNumber: 62,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"sm:text-xs md:text-base lg:text-xl xl:text-2xl font-semibold\",\n                                            children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_5__.formatCryptoAmount)(safeCollateralAmount, collateralCurrency)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                            lineNumber: 68,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-xs text-muted-foreground\",\n                                            children: [\n                                                \"$\",\n                                                safeCollateralValueUSD.toFixed(2),\n                                                \" USDT\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                    lineNumber: 67,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-center rounded-full p-1 border\",\n                            style: {\n                                borderColor: statusStyles.statusBorderColour\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-2 h-2 md:w-3 md:h-3 rounded-full\",\n                                style: {\n                                    backgroundColor: statusStyles.statusColour\n                                }\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                            lineNumber: 76,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-3 mt-3 md:mt-4 space-y-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-4 text-sm\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground mb-1\",\n                                        children: \"Current rate\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"sm:text-xs md:text-sm lg:text-base xl:text-lg font-semibold tracking-tight\",\n                                        children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_5__.formatUSDAmount)(safeCurrentRate)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: ratePair\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground mb-1\",\n                                        children: \"Margin call\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"sm:text-xs md:text-sm lg:text-base xl:text-lg font-semibold tracking-tight\",\n                                        children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_5__.formatUSDAmount)(safeMarginCall)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                        lineNumber: 101,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-xs text-muted-foreground\",\n                                        children: ratePair\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                lineNumber: 99,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                    lineNumber: 87,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n_c = LoanCard;\nvar _c;\n$RefreshReg$(_c, \"LoanCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/loan/LoanCard.tsx\n"));

/***/ })

});