"use client";

import { ArrowRight } from "lucide-react";
import CryptoIcon from "../general/CryptoIcon";
import { Card } from "../ui/card";

interface WalletItemProps {
  name: string;
  symbol: string;
  icon?: string; // Keep for backward compatibility
  color?: string; // Keep for backward compatibility
}

export default function WalletItem({ name, symbol, icon, color }: WalletItemProps) {
  return (
    <Card className="w-full rounded-3xl p-3 bg-[#F8F8F8] hover:bg-muted/50 transition-colors cursor-pointer">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <CryptoIcon 
            symbol={symbol}
            size={20}
            className="w-8 h-8"
          />
          <span className="font-medium text-foreground">{name}</span>
        </div>
        <ArrowRight className="h-4 w-4 text-[#466DFF]" />
      </div>
    </Card>
  );
}