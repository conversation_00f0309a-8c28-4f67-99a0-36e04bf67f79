"use client";

import { Card } from "@/components/ui/card";
import { Calculator, ChevronRight } from "lucide-react";

export function ProfitCalculatorCard() {
  return (
    <Card className="p-6 cursor-pointer hover:bg-gray-50 transition-colors shadow-sm rounded-4xl">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-gray-100 rounded-lg flex items-center justify-center">
            <Calculator className="w-4 h-4 text-gray-600" />
          </div>
          <span className="font-medium text-gray-900">Calculate Your Profit</span>
        </div>
        <ChevronRight className="w-5 h-5 text-gray-400" />
      </div>
    </Card>
  );
}