"use client";

import TransactionItem from "./TransactionItem";

const transactionsData = [
  {
    type: "buy",
    description: "Buy BTC",
    amount: "0.0500 BTC",
    date: "Today",
    icon: "₿",
    iconColor: "#FFFFFF",
    iconBg: "#F7931A"
  },
  {
    type: "loan",
    description: "Get loan",
    amount: "500.00 USDT",
    date: "Today",
    icon: "T",
    iconColor: "#FFFFFF", 
    iconBg: "#26A17B"
  },
  {
    type: "received",
    description: "ETH Received",
    amount: "0.025 ETH",
    date: "Today",
    icon: "◆",
    iconColor: "#FFFFFF",
    iconBg: "#627EEA"
  },
  {
    type: "exchange",
    description: "Exchange",
    amount: "0.0571 ETH",
    date: "Today",
    icon: "🔄",
    iconColor: "#FFFFFF",
    iconBg: "#FF6B6B"
  },
  {
    type: "received",
    description: "BTC Received", 
    amount: "0.0500 BTC",
    date: "May 10",
    icon: "₿",
    iconColor: "#FFFFFF",
    iconBg: "#F7931A"
  },
  {
    type: "loan",
    description: "Get loan",
    amount: "500.00 USDT",
    date: "May 10",
    icon: "T",
    iconColor: "#FFFFFF",
    iconBg: "#26A17B"
  }
];

export default function TransactionsList() {
  return (
    <div className="space-y-1">
      {transactionsData.map((transaction, index) => (
        <TransactionItem
          key={index}
          type={transaction.type}
          description={transaction.description}
          amount={transaction.amount}
          date={transaction.date}
          icon={transaction.icon}
          iconColor={transaction.iconColor}
          iconBg={transaction.iconBg}
        />
      ))}
    </div>
  );
}