"use client";

import Link from "next/link";
import React from "react";
import clsx from "clsx";
import { Card } from "@/components/ui/card";
import { ArrowRight } from "lucide-react";

type Props = {
  href: string;
  label: string;
  icon: React.ReactNode;
  trailing?: React.ReactNode;
  active?: boolean;
};

const SidebarItem: React.FC<Props> = ({ href, label, icon, trailing, active }) => {
  return (
    <Link
      href={href}
      className={clsx(
        "group flex",
        "text-slate-600 hover:text-slate-900",
        "transition-colors items-center"
      )}
    >
      <Card className={`min-w-full min-h-16 h-20 p-0 flex flex-row justify-between gap-3 px-14 border-0 shadow-none ${active ? '' : 'hover:bg-slate-200/30'}`}>
        <div className="flex items-center xl:gap-4 gap-1">
          <div
            className={clsx(
              "grid place-items-center rounded-full",
              "h-10 w-10 transition-colors",
              active
                ? "bg-[#2563EB]"
                : "bg-slate-700 group-hover:bg-slate-800"
            )}
          >
            {icon}
          </div>
          <span className={clsx(
            "text-2xl transition-colors",
            active
              ? "text-[#2563EB] font-semibold"
              : "text-slate-700 group-hover:text-slate-900"
          )}>
            {label}
          </span>
        </div>
        {active ? (
          <div className="opacity-100 flex items-center justify-center">
            <ArrowRight className="h-4 w-4 text-[#466DFF] transition-colors" />
          </div>
        ) : trailing ? (
          <div className="opacity-100">{trailing}</div>
        ) : null}
      </Card>
    </Link>
  );
};

export default SidebarItem;