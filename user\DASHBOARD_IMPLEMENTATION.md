# Dashboard Implementation

## Overview

This implementation adds a toggle functionality between `FirstLandingDash` and `MainDash` components, along with server-side data fetching utilities.

## Key Features

### 1. Toggle Functionality
- **Switch Control**: Added a toggle switch that allows users to switch between the main dashboard and first landing dashboard
- **Automatic Detection**: The system automatically detects if a user has data and shows the appropriate dashboard
- **Manual Override**: Users can manually toggle between dashboards regardless of their data status

### 2. Server-Side Data Fetching
- **Centralized Data Management**: All dashboard data is fetched through server-side utilities
- **Type Safety**: Full TypeScript support with proper interfaces
- **Mock Data Integration**: Seamless integration with existing mock data
- **API Route**: Created `/api/dashboard` endpoint for external data fetching

## File Structure

```
src/
├── lib/
│   └── dashboard-data.ts          # Server-side data fetching utilities
├── app/
│   ├── api/
│   │   └── dashboard/
│   │       └── route.ts           # API endpoint for dashboard data
│   └── page.tsx                   # Updated main page with toggle functionality
└── components/
    └── dashboard/
        ├── MainDash.tsx           # Updated to accept data props
        ├── FirstLandingDash.tsx   # Updated to accept data props
        ├── TopTrendsTable.tsx     # Updated imports
        ├── NewListedCoins.tsx     # Updated imports
        └── LTVChart.tsx           # Updated imports
```

## Data Flow

1. **Page Load**: `page.tsx` calls `fetchDashboardData()` on component mount
2. **Data Fetching**: Server-side utilities determine if user has data
3. **Dashboard Selection**: 
   - If user has data → Show `MainDash` with full data
   - If no data → Show `FirstLandingDash` with landing data
4. **Toggle Override**: User can manually switch between dashboards

## Usage

### Basic Usage
The toggle functionality works automatically based on user data:

```typescript
// Automatically shows appropriate dashboard based on user data
const dashboardData = await fetchDashboardData(userId);
const shouldShowMainDash = dashboardData.hasUserData;
```

### Manual Toggle
Users can manually switch between dashboards:

```typescript
const handleToggleDashboard = () => {
  setShowMainDash(!showMainDash);
};
```

### API Usage
External applications can fetch dashboard data:

```typescript
// GET /api/dashboard?userId=123
const response = await fetch('/api/dashboard?userId=123');
const { data } = await response.json();
```

## Data Types

### DashboardData
```typescript
interface DashboardData {
  hasUserData: boolean;
  interestData: InterestDataPoint[];
  loanData: LoanData[];
  ltvChartData: LTVChartDataPoint[];
  topTrendsData: TopTrendAsset[];
  newCoinsData: NewCoinAsset[];
}
```

### InterestDataPoint
```typescript
interface InterestDataPoint {
  month: string;
  interest: number;
}
```

### LoanData
```typescript
interface LoanData {
  id: number;
  crypto: string;
  icon: string;
  amount: string;
  value: string;
  status: string;
  price: string;
  borderColor: string;
  statusColor: string;
}
```

## Server-Side Functions

### fetchDashboardData(userId?: string)
Fetches all dashboard data for a user, determining if they have transaction data.

### fetchInterestData(period)
Fetches interest data for specific time periods ('all-time', 'yearly', '6months').

### fetchLoanData(userId?: string)
Fetches loan data for a specific user.

### fetchMarketData()
Fetches market data including top trends and new coins.

## Responsive Design

The implementation supports both mobile and desktop layouts:

- **Mobile**: Vertical layout with toggle at the top
- **Desktop**: Grid layout with toggle in the main column

## Future Enhancements

1. **Real API Integration**: Replace mock data with actual API calls
2. **Caching**: Implement data caching for better performance
3. **Real-time Updates**: Add WebSocket support for live data updates
4. **User Preferences**: Save user's dashboard preference
5. **Analytics**: Track dashboard usage and user behavior

## Testing

To test the toggle functionality:

1. Start the development server: `npm run dev`
2. Navigate to the dashboard page
3. Use the toggle switch to switch between dashboards
4. The system will randomly show different data states (with/without user data)

## Notes

- The current implementation uses random data generation for demonstration
- In production, replace the mock data functions with real API calls
- The toggle state is not persisted (resets on page refresh)
- All components maintain their existing functionality while adding the new features
