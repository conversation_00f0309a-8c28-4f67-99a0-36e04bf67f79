"use client";

import { useState } from "react";
import { Bell } from "lucide-react";
import { Button } from "@/components/ui/button";
import NotificationPanel from "./NotificationPanel";

export default function WelcomeHeader() {
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);

  const toggleNotifications = () => {
    setIsNotificationOpen(!isNotificationOpen);
  };

  const closeNotifications = () => {
    setIsNotificationOpen(false);
  };

  return (
    <div className="mb-8">
      <div className="flex items-start justify-between mb-2">
        <div className="flex-1">
          <h1 className="text-3xl text-[#466DFF] mb-2">
            Hello and Welcome!
          </h1>
        </div>
        <div className="relative ml-6">
          <Button
            variant="ghost"
            size="icon"
            className="rounded-lg border-2 border-[#444444] "
            onClick={toggleNotifications}
          >
            <Bell className="h-5 w-5" />
          </Button>
          <NotificationPanel
            isOpen={isNotificationOpen}
            onClose={closeNotifications}
          />
        </div>
      </div>
      <p className="text-muted-foreground text-lg leading-relaxed w-full">
        Dive into the world of crypto lending—secure, simple, and designed for beginners. Start using your digital assets to unlock new earning opportunities today!
      </p>
    </div>
  );
}