"use client";

import CryptoIcon from "../general/CryptoIcon";
import { Card } from "../ui/card";

interface TransactionItemProps {
  type: string;
  description: string;
  amount: string;
  date?: string;
  cryptoSymbol?: string; // New prop for crypto symbol
  icon?: string; // Keep for backward compatibility
  iconColor?: string; // Keep for backward compatibility
  iconBg?: string; // Keep for backward compatibility
}

export default function TransactionItem({
  type,
  description,
  amount,
  date,
  cryptoSymbol,
  icon,
  iconColor,
  iconBg
}: TransactionItemProps) {
  // Function to extract crypto symbol from description or amount
  const getCryptoSymbolFromTransaction = () => {
    if (cryptoSymbol) return cryptoSymbol;

    // Extract from amount (e.g., "0.0500 BTC" -> "BTC")
    const amountMatch = amount.match(/([A-Z]{3,4})$/);
    if (amountMatch) return amountMatch[1];

    // Extract from description (e.g., "Buy BTC" -> "BTC")
    const descMatch = description.match(/\b(BTC|ETH|USDT|ADA|DASH|SOL|XMR|DOT)\b/i);
    if (descMatch) return descMatch[1].toUpperCase();

    return null;
  };

  const detectedCryptoSymbol = getCryptoSymbolFromTransaction();

  return (
    <div className="py-1 border-b border-border/30 last:border-b-0">
      <Card className="w-full rounded-4xl p-4 bg-[#F8F8F8]">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {detectedCryptoSymbol ? (
              <CryptoIcon
                symbol={detectedCryptoSymbol}
                size={20}
                className="w-8 h-8"
              />
            ) : (
              // Fallback to original icon for non-crypto transactions
              <div
                className="w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold"
                style={{ backgroundColor: iconBg, color: iconColor }}
              >
                {icon}
              </div>
            )}
            <div>
              <div className="font-medium text-foreground">{description}</div>
              {/* {date && <div className="text-xs text-muted-foreground">{date}</div>} */}
            </div>
          </div>
          <div className="text-right">
            <div className="font-semibold text-foreground">{amount}</div>
          </div>
        </div>
      </Card>
    </div >
  );
}