// Server-side utilities for dashboard data fetching and processing

import { mockLTVChartData, mockTopTrendsData, mockNewCoinsData } from "@/components/dashboard/dashboardMockData";

// Types for the dashboard data
export interface DashboardData {
  hasUserData: boolean;
  interestData: InterestDataPoint[];
  loanData: LoanData[];
  ltvChartData: LTVChartDataPoint[];
  topTrendsData: TopTrendAsset[];
  newCoinsData: NewCoinAsset[];
}

export interface InterestDataPoint {
  month: string;
  interest: number;
}

export interface LoanData {
  id: number;
  crypto: string;
  icon: string;
  amount: string;
  value: string;
  status: string;
  price: string;
  borderColor: string;
  statusColor: string;
}

export interface LTVChartDataPoint {
  collateralValue: number;
  ltvRatio: number;
}

export interface TopTrendAsset {
  rank: number;
  asset: string;
  symbol: string;
  collateralValue: string;
  volume24h: string;
  icon: string;
  color: string;
}

export interface NewCoinAsset {
  name: string;
  symbol: string;
  icon: string;
  color: string;
}

// Sample interest data for the chart
const sampleInterestData: InterestDataPoint[] = [
  { month: "Jan", interest: 1300 },
  { month: "Feb", interest: 1800 },
  { month: "Mar", interest: 800 },
  { month: "Apr", interest: 1200 },
  { month: "May", interest: 1500 },
  { month: "Jun", interest: 1400 },
  { month: "Jul", interest: 1800 },
  { month: "Aug", interest: 1600 },
  { month: "Sep", interest: 1200 },
  { month: "Oct", interest: 600 },
  { month: "Nov", interest: 300 },
  { month: "Dec", interest: 800 },
];

// Sample loan data
const sampleLoanData: LoanData[] = [
  {
    id: 1,
    crypto: "BTC",
    icon: "₿",
    amount: "0.0025 BTC",
    value: "250 USDT",
    status: "margin_call",
    price: "97,568.21 BTC/USDT",
    borderColor: "border-red-500",
    statusColor: "bg-red-500"
  },
  {
    id: 2,
    crypto: "ETH",
    icon: "◆",
    amount: "0.0025 ETH",
    value: "250 USDT",
    status: "margin_call",
    price: "97,568.21 ETH/USDT",
    borderColor: "border-white",
    statusColor: "bg-green-500"
  },
  {
    id: 3,
    crypto: "ADA",
    icon: "★",
    amount: "0.0025 ADA",
    value: "50 USDT",
    status: "margin_call",
    price: "97,568.21 ADA/USDT",
    borderColor: "border-orange-500",
    statusColor: "bg-orange-500"
  },
  {
    id: 4,
    crypto: "DASH",
    icon: "Ð",
    amount: "0.0025 DASH",
    value: "50 USDT",
    status: "margin_call",
    price: "97,568.21 DASH/USDT",
    borderColor: "border-orange-500",
    statusColor: "bg-orange-500"
  }
];

/**
 * Fetches dashboard data for a user
 * This would typically make API calls to your backend
 */
export async function fetchDashboardData(userId?: string): Promise<DashboardData> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  // Check if user has data (this would be a real API call)
  const hasUserData = await checkUserHasData(userId);

  if (hasUserData) {
    // User has data - return full dashboard data
    return {
      hasUserData: true,
      interestData: sampleInterestData,
      loanData: sampleLoanData,
      ltvChartData: mockLTVChartData,
      topTrendsData: mockTopTrendsData,
      newCoinsData: mockNewCoinsData,
    };
  } else {
    // User has no data - return only landing data
    return {
      hasUserData: false,
      interestData: [],
      loanData: [],
      ltvChartData: mockLTVChartData,
      topTrendsData: mockTopTrendsData,
      newCoinsData: mockNewCoinsData,
    };
  }
}

/**
 * Checks if a user has any transaction data
 * This would be a real API call to your backend
 */
async function checkUserHasData(userId?: string): Promise<boolean> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 50));
  
  // For demo purposes, we'll randomly return true/false
  // In a real app, this would check the database
  return Math.random() > 0.5;
}

/**
 * Fetches interest data for a specific time period
 */
export async function fetchInterestData(period: 'all-time' | 'yearly' | '6months'): Promise<InterestDataPoint[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));

  // In a real app, this would filter data based on the period
  switch (period) {
    case 'yearly':
      return sampleInterestData.slice(-12);
    case '6months':
      return sampleInterestData.slice(-6);
    default:
      return sampleInterestData;
  }
}

/**
 * Fetches loan data for a user
 */
export async function fetchLoanData(userId?: string): Promise<LoanData[]> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // In a real app, this would fetch from your loan service
  return sampleLoanData;
}

/**
 * Fetches market data (trends and new coins)
 */
export async function fetchMarketData(): Promise<{
  topTrends: TopTrendAsset[];
  newCoins: NewCoinAsset[];
}> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  // In a real app, this would fetch from a market data API
  return {
    topTrends: mockTopTrendsData,
    newCoins: mockNewCoinsData,
  };
}
