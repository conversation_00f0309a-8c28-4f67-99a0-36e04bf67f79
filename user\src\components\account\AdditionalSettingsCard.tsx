"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Info, CircleCheck } from "lucide-react";
import { AppSettings, KYCStatus } from "@/types/accountSchema";

interface AdditionalSettingsCardProps {
  appSettings: AppSettings;
  kycStatus: KYCStatus;
  onAppSettingsChange?: (settings: Partial<AppSettings>) => void;
}

export function AdditionalSettingsCard({ 
  appSettings, 
  kycStatus, 
  onAppSettingsChange 
}: AdditionalSettingsCardProps) {
  const handleDisplayModeToggle = (checked: boolean) => {
    onAppSettingsChange?.({ displayMode: checked ? "Clean" : "Standard" });
  };

  return (
    <Card className="bg-white border border-gray-200 h-full">
      <CardContent className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Additional settings</h2>
        
        <div className="space-y-6">
          {/* App display */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label htmlFor="app-display" className="text-gray-700 font-medium">
                App display
              </Label>
              <Switch
                id="app-display"
                checked={appSettings.displayMode === "Clean"}
                onCheckedChange={handleDisplayModeToggle}
              />
            </div>
            <p className="text-sm text-gray-500">{appSettings.displayMode}</p>
          </div>

          {/* Language */}
          <div className="space-y-2">
            <Label className="text-gray-700 font-medium">Language</Label>
            <p className="text-sm text-gray-500">{appSettings.language}</p>
          </div>

          {/* KYC information */}
          <div className="space-y-4 pt-4 border-t border-gray-200">
            <div className="flex items-center gap-2">
              <Label className="text-gray-700 font-medium">KYC information</Label>
              <Info size={16} className="text-gray-400" />
            </div>
            
            <div className="space-y-3">
              <div className="flex items-center gap-3">
                <CircleCheck size={20} className="text-blue-600" />
                <span className="text-sm text-gray-700">National identity number verified</span>
              </div>
              
              <div className="flex items-center gap-3">
                <CircleCheck size={20} className="text-blue-600" />
                <span className="text-sm text-gray-700">Face recognition approved</span>
              </div>
              
              <div className="flex items-center gap-3">
                <CircleCheck size={20} className="text-blue-600" />
                <span className="text-sm text-gray-700">Address verified</span>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}