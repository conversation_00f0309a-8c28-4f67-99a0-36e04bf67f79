"use client"

import { Card, CardContent } from "@/components/ui/card"
import { CryptoCurrency, LoanHealth } from "@/types/enums"
import { getStatusStyles, getStatusFromLoanHealth } from "@/utils/statusStyles"
import CryptoIcon, { getCryptoIconColor } from "../general/CryptoIcon"

interface LoanCardProps {
  id: string;
  tokenTicker: CryptoCurrency;
  loanAmount?: number;
  loanValueUSDT?: number;
  status: LoanHealth;
  marginCallAmount?: number;
}


export default function LoanCard({
  id,
  tokenTicker,
  loanAmount = 0,
  loanValueUSDT = 0,
  status,
  marginCallAmount = 0
}: LoanCardProps) {
  const statusVariant = getStatusFromLoanHealth(status);
  const statusStyles = getStatusStyles(statusVariant);

  // Add safety checks for numeric values
  const safeLoanAmount = typeof loanAmount === 'number' && !isNaN(loanAmount) ? loanAmount : 0;
  const safeLoanValueUSDT = typeof loanValueUSDT === 'number' && !isNaN(loanValueUSDT) ? loanValueUSDT : 0;
  const safeMarginCallAmount = typeof marginCallAmount === 'number' && !isNaN(marginCallAmount) ? marginCallAmount : 0;

  return (
    <Card
      className="relative overflow-hidden rounded-4xl border-2 shadow-sm h-full min-h-0 p-3"
      style={{
        backgroundColor: statusStyles.cardBG,
        borderColor: statusStyles.cardBorderColour,
      }}
    >
      <CardContent className="h-full flex flex-col justify-between">
        <div className="flex items-start justify-between py-3">
          <div className="flex items-center gap-2 md:gap-3">
            <CryptoIcon 
              symbol={tokenTicker}
              size={28}
              className="p-2"
            />
            <div className="flex flex-col">
              <p className="sm:text-xs md:text-base lg:text-xl xl:text-2xl font-semibold">
                {safeLoanAmount} {tokenTicker}
              </p>
              <p className="text-xs text-muted-foreground">
                ${safeLoanValueUSDT.toFixed(2)} USDT
              </p>
            </div>
          </div>
          <div
            className="flex items-center justify-center rounded-full p-1 border"
            style={{ borderColor: statusStyles.statusBorderColour }}
          >
            <div
              className="w-2 h-2 md:w-3 md:h-3 rounded-full"
              style={{ backgroundColor: statusStyles.statusColour }}
            />
          </div>
        </div>

        <div className="mb-3 mt-3 md:mt-4 space-y-1">
          <p className="text-xs text-muted-foreground capitalize">Margin call </p>
          <p className="sm:text-xs md:text-sm lg:text-xl xl:text-xl font-semibold tracking-tight">
            ${safeMarginCallAmount.toLocaleString()} {tokenTicker}/USDT
          </p>
        </div>
      </CardContent>
    </Card>
  );
}