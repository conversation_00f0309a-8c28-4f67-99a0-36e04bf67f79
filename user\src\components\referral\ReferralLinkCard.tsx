"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Copy } from "lucide-react";

interface ReferralLinkCardProps {
  referralLink: string;
  isMobile?: boolean;
}

export function ReferralLinkCard({ referralLink, isMobile = false }: ReferralLinkCardProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(referralLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  return (
    <div>
      <h3 className="font-semibold mb-3 text-gray-900">Your referral link</h3>
      <div className="flex items-center gap-2 p-3 bg-gray-100 rounded-lg">
        <span className={`text-sm text-gray-600 flex-1 ${isMobile ? 'truncate' : ''}`}>
          {referralLink}
        </span>
        <Button 
          variant="outline" 
          size="sm"
          onClick={handleCopy}
          className={`${copied ? "bg-green-100 text-green-700 border-green-300" : "bg-blue-600 text-white border-blue-600 hover:bg-blue-700"} transition-all duration-200`}
        >
          {isMobile ? (
            <Copy className="w-4 h-4" />
          ) : (
            copied ? "COPIED" : "COPY"
          )}
        </Button>
      </div>
    </div>
  );
}