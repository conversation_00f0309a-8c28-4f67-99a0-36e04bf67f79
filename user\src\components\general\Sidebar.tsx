"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";
import { SidebarItem } from "@/components/general";

type Item = {
  href: string;
  label: string;
  icon: React.ReactNode;
  trailing?: React.ReactNode;
};

const Logo = () => {
  return (
    <div className="flex items-center justify-center px-6 py-6 md:py-7 lg:py-8">
      <img 
        src="/images/logoLB.svg" 
        alt="LENDBLOC Logo" 
        className="h-6 w-auto md:h-5 lg:h-16"
      />
    </div>
  );
};

const items: Item[] = [
  {
    href: "/",
    label: "Dashboard",
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
        <rect x="3" y="3" width="8" height="8" rx="2" stroke="white" strokeWidth="1.8"/>
        <rect x="13" y="3" width="8" height="8" rx="2" stroke="white" strokeWidth="1.8"/>
        <rect x="3" y="13" width="8" height="8" rx="2" stroke="white" strokeWidth="1.8"/>
        <rect x="13" y="13" width="8" height="8" rx="2" stroke="white" strokeWidth="1.8"/>
      </svg>
    )
  },
  {
    href: "/loan",
    label: "Loan",
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
        <circle cx="12" cy="12" r="9" stroke="white" strokeWidth="1.8"/>
        <path d="M8 13c1.5 1 6 1 8 0" stroke="white" strokeWidth="1.8" strokeLinecap="round"/>
        <path d="M9 9h6" stroke="white" strokeWidth="1.8" strokeLinecap="round"/>
      </svg>
    ),
  },
  {
    href: "/savings",
    label: "Savings",
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
        <path d="M5 12a7 7 0 0114 0v4H7a2 2 0 01-2-2v-2z" stroke="white" strokeWidth="1.8" fill="none"/>
        <circle cx="16.5" cy="11" r="1" fill="white"/>
        <path d="M4 14h2" stroke="white" strokeWidth="1.8" strokeLinecap="round"/>
      </svg>
    ),
  },
  {
    href: "/referral",
    label: "Referral",
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
        <circle cx="7" cy="8" r="3" stroke="white" strokeWidth="1.8"/>
        <circle cx="17" cy="8" r="3" stroke="white" strokeWidth="1.8"/>
        <path d="M4 18c.6-2.1 2.7-4 6-4s5.4 1.9 6 4" stroke="white" strokeWidth="1.8" strokeLinecap="round"/>
      </svg>
    ),
  },
  {
    href: "/account",
    label: "Account",
    icon: (
      <svg width="22" height="22" viewBox="0 0 24 24" fill="none">
        <circle cx="12" cy="8" r="3.2" stroke="white" strokeWidth="1.8"/>
        <path d="M5 19c0-3.3 3.1-6 7-6s7 2.7 7 6" stroke="white" strokeWidth="1.8" strokeLinecap="round"/>
      </svg>
    ),
  },
];

const Sidebar: React.FC = () => {
  const pathname = usePathname();

  return (
    <aside
      className="
        hidden md:flex md:flex-col
        w-full shrink-0 border-r border-slate-200
        bg-white text-slate-700
        h-screen sticky top-0
      "
    >
      <Link href="/" aria-label="Lendbloc" className="select-none">
        <Logo />
      </Link>

      <nav className="mt-6 xl:mt-12 flex-1">
        <ul className="flex flex-col gap-18">
          {items.map((item) => {
            const active =
              item.href === "/"
                ? pathname === "/"
                : pathname?.startsWith(item.href);
            return (
              <li key={item.href} className="font-semibold">
                <SidebarItem
                  href={item.href}
                  label={item.label}
                  active={!!active}
                  icon={item.icon}
                  trailing={item.trailing}
                />
              </li>
            );
          })}
        </ul>
      </nav>
    </aside>
  );
};

export default Sidebar;