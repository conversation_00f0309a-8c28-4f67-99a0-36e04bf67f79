"use client";

import { Button } from "@/components/ui/button";

interface PeriodFilterProps {
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
  periods?: string[];
}

export function PeriodFilter({ 
  selectedPeriod, 
  onPeriodChange, 
  periods = ["All time", "Yearly", "6 Months"] 
}: PeriodFilterProps) {
  return (
    <div className="flex gap-4">
      {periods.map((period) => (
        <button
          key={period}
          onClick={() => onPeriodChange(period)}
          className={`text-sm font-medium pb-1 transition-colors ${
            selectedPeriod === period
              ? "text-blue-600 border-b-2 border-blue-600"
              : "text-gray-500 hover:text-gray-700"
          }`}
        >
          {period}
        </button>
      ))}
    </div>
  );
}