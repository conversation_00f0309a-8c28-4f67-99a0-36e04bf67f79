// Mock data for the dashboard components

export const mockLTVChartData = [
  { collateralValue: 2000, ltvRatio: 45 },
  { collateralValue: 4000, ltvRatio: 35 },
  { collateralValue: 6000, ltvRatio: 25 },
  { collateralValue: 8000, ltvRatio: 20 },
  { collateralValue: 10000, ltvRatio: 15 }
];

export const mockTopTrendsData = [
  {
    rank: 1,
    asset: "Bitcoin",
    symbol: "BTC",
    collateralValue: "$80,125.00",
    volume24h: "$4,589.00",
    icon: "₿",
    color: "#F7931A"
  },
  {
    rank: 2, 
    asset: "Ethereum",
    symbol: "ETH",
    collateralValue: "$80,125.00",
    volume24h: "$4,589.00",
    icon: "◆",
    color: "#627EEA"
  },
  {
    rank: 3,
    asset: "Solana", 
    symbol: "SOL",
    collateralValue: "$80,125.00",
    volume24h: "$4,589.00",
    icon: "◎",
    color: "#9945FF"
  },
  {
    rank: 4,
    asset: "Monero",
    symbol: "XMR", 
    collateralValue: "$80,125.00",
    volume24h: "$4,589.00",
    icon: "ɱ",
    color: "#FF6600"
  },
  {
    rank: 5,
    asset: "Polkadot",
    symbol: "DOT",
    collateralValue: "$80,125.00", 
    volume24h: "$4,589.00",
    icon: "●",
    color: "#E6007A"
  }
];

export const mockNewCoinsData = [
  {
    name: "Ecash",
    symbol: "ECH",
    icon: "E",
    color: "#00D4AA"
  },
  {
    name: "Decentraland", 
    symbol: "MANA",
    icon: "M",
    color: "#FF2D55"
  },
  {
    name: "Chainlink",
    symbol: "LINK", 
    icon: "⬡",
    color: "#375BD2"
  },
  {
    name: "PEPE",
    symbol: "PEPE",
    icon: "🐸",
    color: "#4CAF50"
  }
];

// Type definitions for the dashboard components
export interface LTVChartDataPoint {
  collateralValue: number;
  ltvRatio: number;
}

export interface TopTrendAsset {
  rank: number;
  asset: string;
  symbol: string;
  collateralValue: string;
  volume24h: string;
  icon: string;
  color: string;
}

export interface NewCoinAsset {
  name: string;
  symbol: string;
  icon: string;
  color: string;
}

export interface FirstLandingDashProps {
  ltvChartData?: LTVChartDataPoint[];
  topTrends?: TopTrendAsset[];
  newCoins?: NewCoinAsset[];
}