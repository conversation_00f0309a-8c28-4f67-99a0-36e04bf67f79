"use client";

import React, { useState } from 'react';
import { OnboardingSlide } from './OnboardingSlide';
import { OnboardingSlideData } from '../../types/schema';

interface SavingsOnboardingProps {
  slides: OnboardingSlideData[];
  onComplete: () => void;
}

export const SavingsOnboarding: React.FC<SavingsOnboardingProps> = ({ slides, onComplete }) => {
  const [currentSlide, setCurrentSlide] = useState(0);

  const handleNext = () => {
    if (currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    } else {
      onComplete();
    }
  };

  return (
    <div className=" flex flex-col items-center justify-center">
      <div className="w-full max-w-6xl mx-auto">
        <div className="mb-2 pl-6">
          <h1 className="text-3xl font-bold text-gray-800 mb-4">
            Welcome to Savings Account!
          </h1>
        </div>
        
        <OnboardingSlide
          slide={slides[currentSlide]}
          onNext={handleNext}
          slides={slides}
          currentSlide={currentSlide}
        />
      </div>
      
      {/* APY Information Section */}
      <div className="w-full max-w-4xl mx-auto mt-12 p-6">
        <div className="bg-white rounded-2xl p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">What is APY?</h3>
          <p className="text-gray-700 leading-relaxed">
            APY <strong>(Annual Percentage Yield)</strong> represents the total annual return on your deposited 
            cryptocurrency, taking into account the powerful effect of compound interest. Unlike APR (Annual 
            Percentage Rate), which only reflects a <strong>simple interest rate</strong>, APY includes the 
            interest earned on your initial deposit as well as the interest that has accumulated over time. 
            This means that if your
          </p>
        </div>
      </div>
    </div>
  );
};