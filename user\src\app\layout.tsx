"use client";

// import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import { AppProvider } from "@/hooks/context/useAppContext";
import dynamic from "next/dynamic";
import { usePathname } from "next/navigation";
import { useViewportType } from "@/hooks/use-viewport";
import React from "react";
import { Sidebar } from "@/components/general";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

// export const metadata: Metadata = {
//   title: "Create Next App",
//   description: "Generated by create next app",
// }

const ClientAuthGate = dynamic(() => import("./private/AuthGate").then((m) => m.AuthGate), {
  ssr: false,
});

function Shell({ children }: { children: React.ReactNode }) {
  const { isMobile, isTablet } = useViewportType();
  const pathname = usePathname();

  // Hide sidebars and mobile navbar on /auth
  const hideChrome = pathname === "/auth";

  // Centralize responsive tokens to improve readability and maintainability.
  // Prefer explicit branches to nested ternaries for clarity.
  const layout = (() => {
    if (hideChrome) {
      return {
        containerClass: "min-h-screen",
        sidebarVisible: false as const,
        sidebarMinWidth: "0",
        contentMinWidth: "100vw",
        contentPaddingBottom: "0px",
        showMobileNav: false as const,
      };
    }

    if (isMobile) {
      return {
        containerClass: "min-h-screen",
        sidebarVisible: false as const,
        sidebarMinWidth: "0",
        contentMinWidth: "100vw",
        // Reserve space for fixed mobile navbar height (e.g., 64px)
        contentPaddingBottom: "64px",
        showMobileNav: true as const,
      };
    }
    if (isTablet) {
      return {
        containerClass: "min-h-screen flex",
        sidebarVisible: true as const,
        sidebarMinWidth: "20vw",
        contentMinWidth: "80vw",
        contentPaddingBottom: "0px",
        showMobileNav: false as const,
      };
    }
    // Desktop default
    return {
      containerClass: "min-h-screen flex",
      sidebarVisible: true as const,
      sidebarMinWidth: "25vw",
      contentMinWidth: "75vw",
      contentPaddingBottom: "0px",
      showMobileNav: false as const,
    };
  })();

  // Memoize style objects to avoid re-renders in children that rely on shallow prop checks.
  const sidebarStyle = React.useMemo(
    () => ({ minWidth: layout.sidebarMinWidth }),
    [layout.sidebarMinWidth]
  );
  const contentStyle = React.useMemo(
    () => ({ minWidth: layout.contentMinWidth, paddingBottom: layout.contentPaddingBottom }),
    [layout.contentMinWidth, layout.contentPaddingBottom]
  );

  return (
    <div className={layout.containerClass}>
      {layout.sidebarVisible && (
        <div style={sidebarStyle} aria-hidden={false}>
          <Sidebar />
        </div>
      )}
      <main
        className="min-h-screen"
        style={contentStyle}
        role="main"
      >
        {children}
      </main>

      {/* Fixed bottom navbar visible only on mobile and not on /auth */}
      {layout.showMobileNav && (
        <nav
          className="fixed bottom-0 left-0 right-0 z-50 bg-white/90 backdrop-blur supports-[backdrop-filter]:bg-white/60 pb-4"
          aria-label="Mobile navigation"
        >
          <ul className="flex items-center justify-around text-sm">
            <li>
              <a href="/" className="flex flex-col items-center gap-1">
                <span className="i-lucide-home h-5 w-5" aria-hidden="true" />
                <span>Home</span>
              </a>
            </li>
            <li>
              <a href="/savings" className="flex flex-col items-center gap-1">
                <span className="i-lucide-piggy-bank h-5 w-5" aria-hidden="true" />
                <span>Savings</span>
              </a>
            </li>
            <li>
              <a href="/loan" className="flex flex-col items-center gap-1">
                <span className="i-lucide-banknote h-5 w-5" aria-hidden="true" />
                <span>Loan</span>
              </a>
            </li>
            <li>
              <a href="/account" className="flex flex-col items-center gap-1">
                <span className="i-lucide-user h-5 w-5" aria-hidden="true" />
                <span>Account</span>
              </a>
            </li>
          </ul>
        </nav>
      )}
    </div>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
        <AppProvider>
          <ClientAuthGate>
            <Shell>{children}</Shell>
          </ClientAuthGate>
        </AppProvider>
      </body>
    </html>
  );
}
