"use client";

import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

export default function TransactionFilter() {
  return (
    <div className="w-32">
      <Select defaultValue="all">
        <SelectTrigger className="w-full h-12 rounded-3xl" style={{ height: '3rem' }}>
          <SelectValue placeholder="Filter" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All</SelectItem>
          <SelectItem value="buy">Buy</SelectItem>
          <SelectItem value="sell">Sell</SelectItem>
          <SelectItem value="loan">Loan</SelectItem>
          <SelectItem value="received">Received</SelectItem>
          <SelectItem value="exchange">Exchange</SelectItem>
        </SelectContent>
      </Select>
    </div>
  );
}