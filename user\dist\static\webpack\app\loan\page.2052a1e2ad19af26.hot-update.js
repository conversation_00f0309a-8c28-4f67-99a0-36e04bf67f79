"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/loan/page",{

/***/ "(app-pages-browser)/./src/components/loan/LoanCard.tsx":
/*!******************************************!*\
  !*** ./src/components/loan/LoanCard.tsx ***!
  \******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LoanCard: () => (/* binding */ LoanCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./src/components/ui/card.tsx\");\n/* harmony import */ var _types_enums__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/types/enums */ \"(app-pages-browser)/./src/types/enums.ts\");\n/* harmony import */ var _utils_statusStyles__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/utils/statusStyles */ \"(app-pages-browser)/./src/utils/statusStyles.ts\");\n/* harmony import */ var _utils_formatters__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/utils/formatters */ \"(app-pages-browser)/./src/utils/formatters.ts\");\n/* harmony import */ var _general_CryptoIcon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../general/CryptoIcon */ \"(app-pages-browser)/./src/components/general/CryptoIcon.tsx\");\n/* __next_internal_client_entry_do_not_use__ LoanCard auto */ \n\n\n\n\n\n\nconst LoanCard = (param)=>{\n    let { loan } = param;\n    const { id, collateralAmount, collateralCurrency, collateralValueUSD, currentRate, marginCall, ratePair, health, isHighlighted = false } = loan;\n    // Ensure health is properly defined with fallback\n    const safeHealth = health || _types_enums__WEBPACK_IMPORTED_MODULE_3__.LoanHealth.YELLOW;\n    const statusVariant = (0,_utils_statusStyles__WEBPACK_IMPORTED_MODULE_4__.getStatusFromLoanHealth)(safeHealth);\n    const statusStyles = (0,_utils_statusStyles__WEBPACK_IMPORTED_MODULE_4__.getStatusStyles)(statusVariant);\n    // Add safety checks for numeric values\n    const safeCollateralAmount = typeof collateralAmount === 'number' && !isNaN(collateralAmount) ? collateralAmount : 0;\n    const safeCollateralValueUSD = typeof collateralValueUSD === 'number' && !isNaN(collateralValueUSD) ? collateralValueUSD : 0;\n    const safeCurrentRate = typeof currentRate === 'number' && !isNaN(currentRate) ? currentRate : 0;\n    const safeMarginCall = typeof marginCall === 'number' && !isNaN(marginCall) ? marginCall : 0;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.Card, {\n        className: \"relative overflow-hidden rounded-4xl border-2 shadow-sm transition-all duration-200 hover:shadow-lg \".concat(isHighlighted ? 'ring-2 ring-red-200' : ''),\n        style: {\n            backgroundColor: isHighlighted ? '#FFF2F2' : statusStyles.cardBG,\n            borderColor: isHighlighted ? '#FF5353' : statusStyles.cardBorderColour\n        },\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_2__.CardContent, {\n            className: \"p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-3 gap-4 items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_general_CryptoIcon__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                symbol: collateralCurrency,\n                                size: 32,\n                                className: \"flex-shrink-0\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                lineNumber: 63,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col min-w-0\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold truncate\",\n                                        children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_5__.formatCryptoAmount)(safeCollateralAmount, collateralCurrency)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                        lineNumber: 69,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground truncate\",\n                                        children: [\n                                            safeCollateralValueUSD.toFixed(0),\n                                            \" USDT\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                        lineNumber: 72,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                lineNumber: 68,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                        lineNumber: 62,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-semibold\",\n                                children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_5__.formatUSDAmount)(safeCurrentRate)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-muted-foreground\",\n                                children: ratePair\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                lineNumber: 83,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-right flex-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: (0,_utils_formatters__WEBPACK_IMPORTED_MODULE_5__.formatUSDAmount)(safeMarginCall)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-muted-foreground\",\n                                        children: ratePair\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center rounded-full p-1 border ml-3\",\n                                style: {\n                                    borderColor: statusStyles.statusBorderColour\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-3 h-3 rounded-full\",\n                                    style: {\n                                        backgroundColor: statusStyles.statusColour\n                                    }\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                    lineNumber: 102,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                                lineNumber: 98,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n                lineNumber: 60,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\001WorkProject\\\\lendbloc\\\\user\\\\src\\\\components\\\\loan\\\\LoanCard.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, undefined);\n};\n_c = LoanCard;\nvar _c;\n$RefreshReg$(_c, \"LoanCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/loan/LoanCard.tsx\n"));

/***/ })

});