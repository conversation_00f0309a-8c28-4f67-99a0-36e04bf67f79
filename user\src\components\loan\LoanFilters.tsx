"use client";

import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Download, Search } from 'lucide-react';
import { LoanHealth } from '../../types/enums';

interface LoanFiltersProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  healthFilter: string;
  onHealthFilterChange: (value: string) => void;
  onGetLoan: () => void;
  onDownloadCSV: () => void;
}

export const LoanFilters: React.FC<LoanFiltersProps> = ({
  searchTerm,
  onSearchChange,
  healthFilter,
  onHealthFilterChange,
  onGetLoan,
  onDownloadCSV,
}) => {
  return (
    <div className="flex flex-col gap-4 mb-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold text-blue-600">My loans</h1>
        <Button 
          onClick={onDownloadCSV}
          variant="outline"
          size="sm"
          className="gap-2"
        >
          <Download className="w-4 h-4" />
          Download CSV
        </Button>
      </div>
      
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
        <Button 
          onClick={onGetLoan}
          className="bg-blue-600 hover:bg-blue-700 gap-2 px-6"
        >
          <Plus className="w-4 h-4" />
          Get loan
        </Button>
        
        <div className="relative flex-1 max-w-md">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
          <Input
            placeholder="Find loan"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="pl-10 bg-gray-100 border-gray-200"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Health:</span>
          <Select value={healthFilter} onValueChange={onHealthFilterChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value={LoanHealth.GREEN}>Green</SelectItem>
              <SelectItem value={LoanHealth.YELLOW}>Yellow</SelectItem>
              <SelectItem value={LoanHealth.RED}>Red</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
};