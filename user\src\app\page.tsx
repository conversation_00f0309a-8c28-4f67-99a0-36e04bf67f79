"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger, TabsContent } from "@/components/ui/tabs";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import FirstLandingDash from "@/components/dashboard/FirstLandingDash";
import TopTrendsTable from "@/components/dashboard/TopTrendsTable";
import NewListedCoins from "@/components/dashboard/NewListedCoins";
import { useViewportType } from "@/hooks/use-viewport";
import MainDash from "@/components/dashboard/MainDash";
import AccountTabContent from "@/components/dashboard/AccountTabContent";
import TransactionsTabContent from "@/components/dashboard/TransactionsTabContent";
import { fetchDashboardData, DashboardData } from "@/data/dashboard-data";
import { mockInterestData, mockLoanData } from "@/data/loanDashboardMockData"

export default function DashboardHomePage() {
  const { isMobile, isTablet, isDesktop } = useViewportType();
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [showMainDash, setShowMainDash] = useState(false);
  const [forceToggle, setForceToggle] = useState(false);

  // Fetch dashboard data on component mount
  useEffect(() => {
    const loadDashboardData = async () => {
      try {
        setLoading(true);
        const data = await fetchDashboardData();
        setDashboardData(data);
        setShowMainDash(data.hasUserData);
      } catch (error) {
        console.error('Failed to load dashboard data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadDashboardData();
  }, []);

  // Handle toggle between dashboards
  const handleToggleDashboard = () => {
    setForceToggle(!forceToggle);
    setShowMainDash(!showMainDash);
  };

  // Determine which dashboard to show
  const shouldShowMainDash = forceToggle ? showMainDash : (dashboardData?.hasUserData ?? false);

  if (loading) {
    return (
      <div className="w-full min-h-screen p-2 sm:p-4 bg-gray-300/50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 sm:h-12 sm:w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-sm sm:text-base">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="w-full min-h-screen p-2 sm:p-4 bg-gray-300/50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-red-600 text-sm sm:text-base">Failed to load dashboard data</p>
        </div>
      </div>
    );
  }

  // Mobile and Tablet Layout (< 1200px)
  if (isMobile || isTablet) {
    return (
      <div className="w-full h-screen p-2 sm:p-4 bg-gray-300/50 overflow-hidden">
        <div className="w-full h-full flex flex-col gap-2 sm:gap-4 overflow-hidden">
          {/* Toggle Control */}
          <div className="flex-shrink-0 p-2 sm:p-3 bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <Label htmlFor="dashboard-toggle" className="text-xs sm:text-sm font-medium">
                {shouldShowMainDash ? "Main Dashboard" : "First Landing"}
              </Label>
              <Switch
                id="dashboard-toggle"
                checked={shouldShowMainDash}
                onCheckedChange={handleToggleDashboard}
              />
            </div>
          </div>

          {/* Dashboard Section */}
          <div className="flex-1 min-h-0 max-h-full overflow-hidden">
            {shouldShowMainDash ? (
              <MainDash
                interestData={mockInterestData}
                loanData={mockLoanData}
              />
            ) : (
              <FirstLandingDash
                ltvChartData={dashboardData.ltvChartData}
              />
            )}
          </div>

          {/* Tabs Section */}
          <div className="flex-1 min-h-0 max-h-full overflow-hidden">
            <Card className="h-full p-2 sm:p-4 overflow-hidden">
              <Tabs defaultValue="updates" className="h-full flex flex-col overflow-hidden">
                <TabsList className="grid w-full grid-cols-3 mb-2 sm:mb-4 bg-gray-100 p-1 rounded-full h-10 sm:h-12 flex-shrink-0">
                  <TabsTrigger
                    value="updates"
                    className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm"
                  >
                    Updates
                  </TabsTrigger>
                  <TabsTrigger
                    value="account"
                    className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm"
                  >
                    Account
                  </TabsTrigger>
                  <TabsTrigger
                    value="transactions"
                    className="data-[state=active]:bg-blue-600 data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs sm:text-sm"
                  >
                    Transactions
                  </TabsTrigger>
                </TabsList>

                <div className="flex-1 min-h-0 max-h-full overflow-hidden">
                  <TabsContent value="updates" className="h-full overflow-y-auto space-y-2 sm:space-y-4 px-1 sm:px-3">
                    <div className="space-y-2 sm:space-y-4">
                      <TopTrendsTable trends={dashboardData.topTrendsData} />
                      <NewListedCoins coins={dashboardData.newCoinsData} />
                    </div>
                  </TabsContent>

                  <TabsContent value="account" className="h-full overflow-y-auto space-y-2 sm:space-y-4 px-1 sm:px-3">
                    <AccountTabContent />
                  </TabsContent>

                  <TabsContent value="transactions" className="h-full overflow-y-auto space-y-2 sm:space-y-4 px-1 sm:px-3">
                    <TransactionsTabContent />
                  </TabsContent>
                </div>
              </Tabs>
            </Card>
          </div>
        </div>
      </div>
    )
  }

  // Desktop Layout (≥ 1200px)
  return (
    <div className="w-full h-screen p-4 lg:p-6 bg-gray-300/50 overflow-hidden">
      <div className="w-full h-full grid grid-cols-1 gap-4 lg:gap-6 xl:grid-cols-[6fr_4fr] overflow-hidden">
        {/* Main Dashboard Section */}
        <div className="min-h-0 max-h-full flex flex-col overflow-hidden">
          {/* Toggle Control */}
          <div className="flex-shrink-0 mb-4 lg:mb-6 p-3 lg:p-4 bg-white rounded-lg shadow-sm">
            <div className="flex items-center justify-between">
              <Label htmlFor="dashboard-toggle-desktop" className="text-sm lg:text-base font-medium">
                {shouldShowMainDash ? "Main Dashboard" : "First Landing"}
              </Label>
              <Switch
                id="dashboard-toggle-desktop"
                checked={shouldShowMainDash}
                onCheckedChange={handleToggleDashboard}
              />
            </div>
          </div>

          {/* Dashboard Content */}
          <div className="flex-1 min-h-0 max-h-full overflow-hidden">
            {shouldShowMainDash ? (
              <MainDash
                interestData={mockInterestData}
                loanData={mockLoanData}
              />
            ) : (
              <FirstLandingDash
                ltvChartData={dashboardData.ltvChartData}
              />
            )}
          </div>
        </div>

        {/* Sidebar Section */}
        <div className="min-h-0 max-h-full w-full overflow-hidden">
          <Card className="h-full w-full p-3 lg:p-4 xl:p-6 overflow-hidden">
            <Tabs defaultValue="updates" className="h-full flex flex-col bg-transparent overflow-hidden">
              <TabsList className="grid w-full grid-cols-3 mb-4 lg:mb-6 p-1 bg-transparent rounded-full h-10 lg:h-12 flex-shrink-0">
                <TabsTrigger
                  value="updates"
                  className="data-[state=active]:bg-[#466DFF] data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs lg:text-sm hover:cursor-pointer"
                >
                  Updates
                </TabsTrigger>
                <TabsTrigger
                  value="account"
                  className="data-[state=active]:bg-[#466DFF] data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs lg:text-sm hover:cursor-pointer"
                >
                  Account
                </TabsTrigger>
                <TabsTrigger
                  value="transactions"
                  className="data-[state=active]:bg-[#466DFF] data-[state=active]:text-white data-[state=active]:shadow-sm rounded-full text-xs lg:text-sm hover:cursor-pointer"
                >
                  Transactions
                </TabsTrigger>
              </TabsList>

              <div className="flex-1 min-h-0 max-h-full overflow-hidden">
                <TabsContent value="updates" className="h-full overflow-y-auto space-y-3 lg:space-y-4 px-1 lg:px-3">
                  <div className="space-y-3 lg:space-y-4">
                    <TopTrendsTable trends={dashboardData.topTrendsData} />
                    <NewListedCoins coins={dashboardData.newCoinsData} />
                  </div>
                </TabsContent>

                <TabsContent value="account" className="h-full overflow-y-auto space-y-3 lg:space-y-4 px-1 lg:px-3">
                  <AccountTabContent />
                </TabsContent>

                <TabsContent value="transactions" className="h-full overflow-y-auto space-y-3 lg:space-y-4 px-1 lg:px-3">
                  <TransactionsTabContent />
                </TabsContent>
              </div>
            </Tabs>
          </Card>
        </div>
      </div>
    </div>
  );
}