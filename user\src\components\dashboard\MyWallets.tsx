"use client";

import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import WalletItem from "./WalletItem";

const walletData = [
  {
    name: "Bitcoin",
    symbol: "BTC",
    icon: "₿",
    color: "#F7931A"
  },
  {
    name: "Ethereum", 
    symbol: "ETH",
    icon: "◆",
    color: "#627EEA"
  }
];

export default function MyWallets() {
  return (
    <div className="shadow-none border-0 w-full py-2">
      <div className="pb-4">
        <h2 className="text-lg font-semibold">My wallets</h2>
      </div>
      <div className="space-y-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
          <Input 
            placeholder="Search wallet" 
            className="pl-10 bg-muted/30 border-border h-12 rounded-3xl"
          />
        </div>
        <div className="space-y-2">
          {walletData.map((wallet) => (
            <WalletItem
              key={wallet.symbol}
              name={wallet.name}
              symbol={wallet.symbol}
              icon={wallet.icon}
              color={wallet.color}
            />
          ))}
        </div>
      </div>
    </div>
  );
}