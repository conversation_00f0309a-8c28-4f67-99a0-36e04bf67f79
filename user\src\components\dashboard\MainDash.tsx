"use client"

import { useState } from "react"
import { BellIcon, PlusIcon } from "lucide-react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart"
import { Line, LineChart, XAxis, YAxis, CartesianGrid } from "recharts"
import NotificationPanel from "./NotificationPanel"
import { InterestDataPoint, LoanData } from "@/data/loanDashboardMockData"
import LoanCard from "./LoanCard"

const chartConfig = {
  interest: {
    label: "Interest Earned",
    color: "#10b981", // Green color for the line
  }
}

interface MainDashProps {
  interestData: InterestDataPoint[];
  loanData: LoanData[];
}

export default function MainDash({ interestData, loanData }: MainDashProps) {
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);

  const toggleNotifications = () => {
    setIsNotificationOpen(!isNotificationOpen);
  };

  const closeNotifications = () => {
    setIsNotificationOpen(false);
  };

  return (
    <div className="w-full h-full flex flex-col gap-4 p-3 md:p-4 overflow-hidden">
      {/* Interest Earned Section */}
      <Card className="flex-1 min-h-0 border-2 border-[#2F6FED]/70 shadow-sm rounded-3xl bg-white">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 pt-4 px-4 md:px-6">
          <CardTitle className="text-sm md:text-base font-semibold text-foreground pl-2 md:pl-3">
            Interest earned all time
          </CardTitle>
          <div className="flex items-center gap-2 md:gap-4">
            <Tabs defaultValue="all-time" className="w-auto">
              <TabsList className="grid grid-cols-3 gap-1 md:gap-2 bg-transparent p-0">
                {["all-time", "yearly", "6months"].map((v) => (
                  <TabsTrigger
                    key={v}
                    value={v}
                    className="data-[state=active]:font-semibold data-[state=active]:text-[#2F6FED] text-muted-foreground px-2 md:px-3 py-1 rounded-full transition-colors text-xs md:text-sm"
                  >
                    <span className="mr-1 md:mr-2 capitalize">
                      {v === "all-time" ? "All time" : v === "6months" ? "6 Months" : "Yearly"}
                    </span>
                    <span className="hidden sm:inline-block">
                      <span className="inline-block size-2 rounded-full bg-transparent data-[state=active]:bg-[#2F6FED] translate-y-[-1px]"></span>
                    </span>
                  </TabsTrigger>
                ))}
              </TabsList>
            </Tabs>
            <div className="relative">
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full border border-border h-8 w-8 md:h-9 md:w-9"
                onClick={toggleNotifications}
              >
                <BellIcon className="h-4 w-4 md:h-5 md:w-5" />
              </Button>
              <NotificationPanel
                isOpen={isNotificationOpen}
                onClose={closeNotifications}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent className="px-2 pb-2 h-full min-h-0">
          <div className="w-full h-full rounded-xl border border-none bg-muted/20 p-2">
            {/* Chart container that dynamically resizes */}
            <div className="w-full h-full min-h-0 rounded-lg bg-background overflow-hidden">
              <ChartContainer config={chartConfig} className="h-full w-full">
                <LineChart data={interestData} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
                  <CartesianGrid vertical={false} stroke="#e5e7eb" />
                  <XAxis
                    dataKey="month"
                    tickLine={false}
                    axisLine={{ stroke: '#d1d5db' }}
                    tickMargin={8}
                  />
                  <YAxis
                    tickLine={false}
                    axisLine={{ stroke: '#d1d5db' }}
                    tickFormatter={(val) => `$${val}`}
                    width={60}
                    domain={[100, 'auto']}
                  />
                  <ChartTooltip
                    cursor={{ stroke: '#34d399', strokeOpacity: 0.15 }}
                    content={<ChartTooltipContent />}
                  />
                  <Line
                    type="monotone"
                    dataKey="interest"
                    stroke={chartConfig.interest.color}
                    strokeWidth={3}
                    dot={false}
                    activeDot={{ r: 4 }}
                  />
                </LineChart>
              </ChartContainer>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Loan Insights Section */}
      <div className="flex items-center justify-between flex-shrink-0">
        <h2 className="text-base md:text-lg font-semibold text-foreground">Loan insights</h2>
        <Button className="flex items-center font-semibold gap-2 rounded-full px-3 md:px-4 text-sm md:text-base h-8 md:h-9 bg-[#466DFF]">
          <PlusIcon className="h-3 w-3 md:h-4 md:w-4 font-bold" />
          Get loan
        </Button>
      </div>

      {/* Loan Cards Grid - dynamically resizing */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-4 flex-1 min-h-0">
        {loanData.map((loan) => (
          <LoanCard
            key={loan.id}
            id={loan.id}
            tokenTicker={loan.tokenTicker}
            loanAmount={loan.loanAmount}
            loanValueUSDT={loan.loanValueUSDT}
            status={loan.status}
            marginCallAmount={loan.marginCallAmount}
          />
        ))}
      </div>
    </div >
  )
}