export type ViewportType = 'mobile' | 'tablet' | 'desktop';

/**
 * Determine viewport type from a given width.
 * Breakpoints:
 * - mobile: < 768
 * - tablet: 768 - 1023
 * - desktop: ≥ 1024
 */
export function getViewportType(width: number): ViewportType {
  if (width < 768) return 'mobile';
  if (width < 1200) return 'tablet';
  return 'desktop';
}

/**
 * Get viewport type from current window.innerWidth.
 * Safe on server: returns 'desktop' as a sensible default.
 */
export function getCurrentViewportType(): ViewportType {
  if (typeof window === 'undefined') return 'desktop';
  return getViewportType(window.innerWidth);
}

export function isMobile(width: number): boolean {
  return width < 768;
}

export function isTablet(width: number): boolean {
  return width >= 768 && width < 1200;
}

export function isDesktop(width: number): boolean {
  return width >= 1200;
}

/**
 * Convenience flags from current window width.
 * Safe on server: desktop defaults.
 */
export function getCurrentViewportFlags() {
  const type = getCurrentViewportType();
  return {
    type,
    isMobile: type === 'mobile',
    isTablet: type === 'tablet',
    isDesktop: type === 'desktop',
  };
}