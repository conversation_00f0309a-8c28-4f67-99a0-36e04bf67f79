"use client";

import React from 'react';
import { Bitcoin, Zap } from 'lucide-react';
import { TopTrendItem as TopTrendItemType } from '../../types/schema';
import { SavingsCryptoCurrency } from '../../types/enums';
import { formatRank, formatVolume } from '../../utils/formatters';

interface TopTrendItemProps {
  item: TopTrendItemType;
}

const getCryptocurrencyIcon = (currency: SavingsCryptoCurrency) => {
  switch (currency) {
    case SavingsCryptoCurrency.BTC:
      return <div className="w-6 h-6 rounded-full bg-orange-500 flex items-center justify-center">
        <Bitcoin className="w-4 h-4 text-white" />
      </div>;
    case SavingsCryptoCurrency.ETH:
      return <div className="w-6 h-6 rounded-full bg-gray-500 flex items-center justify-center">
        <Zap className="w-4 h-4 text-white" />
      </div>;
    case SavingsCryptoCurrency.SOL:
      return <div className="w-6 h-6 rounded-full bg-purple-500 flex items-center justify-center text-white font-bold text-xs">
        S
      </div>;
    case SavingsCryptoCurrency.MONERO:
      return <div className="w-6 h-6 rounded-full bg-red-500 flex items-center justify-center text-white font-bold text-xs">
        M
      </div>;
    case SavingsCryptoCurrency.POLKADOT:
      return <div className="w-6 h-6 rounded-full bg-pink-500 flex items-center justify-center text-white font-bold text-xs">
        P
      </div>;
    default:
      return <div className="w-6 h-6 rounded-full bg-gray-400" />;
  }
};

export const TopTrendItem: React.FC<TopTrendItemProps> = ({ item }) => {
  return (
    <tr className="border-b border-gray-100">
      <td className="py-3 px-2 text-sm font-medium">
        {formatRank(item.rank)}
      </td>
      <td className="py-3 px-2">
        <div className="flex items-center gap-2">
          {getCryptocurrencyIcon(item.asset)}
          <span className="text-sm font-medium">{item.asset}</span>
        </div>
      </td>
      <td className="py-3 px-2 text-sm font-medium">
        {formatVolume(item.collateralValue)}
      </td>
      <td className="py-3 px-2 text-sm font-medium">
        {formatVolume(item.volume24h)}
      </td>
    </tr>
  );
};