"use client";

import { Card, CardContent } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { ChevronRight } from "lucide-react";
import { PrivacySettings } from "@/types/accountSchema";

interface PrivacySettingsCardProps {
  settings: PrivacySettings;
  onSettingsChange?: (settings: Partial<PrivacySettings>) => void;
}

export function PrivacySettingsCard({ settings, onSettingsChange }: PrivacySettingsCardProps) {
  const handleToggle = (key: keyof PrivacySettings, value: boolean) => {
    onSettingsChange?.({ [key]: value });
  };

  return (
    <Card className="bg-white border border-gray-200">
      <CardContent className="p-6">
        <h2 className="text-lg font-semibold text-gray-900 mb-6">Privacy and Security</h2>
        
        <div className="space-y-6">
          {/* Show assets value */}
          <div className="flex items-center justify-between">
            <Label htmlFor="show-assets" className="text-gray-700 font-medium">
              Show assets value
            </Label>
            <Switch
              id="show-assets"
              checked={settings.showAssetsValue}
              onCheckedChange={(checked) => handleToggle('showAssetsValue', checked)}
            />
          </div>

          {/* 2 Factor authentication */}
          <div className="flex items-center justify-between">
            <Label htmlFor="two-factor" className="text-gray-700 font-medium">
              2 Factor authentication
            </Label>
            <Switch
              id="two-factor"
              checked={settings.twoFactorAuth}
              onCheckedChange={(checked) => handleToggle('twoFactorAuth', checked)}
            />
          </div>

          {/* Change email address */}
          <div className="flex items-center justify-between py-2 cursor-pointer hover:bg-gray-50 -mx-2 px-2 rounded-md transition-colors">
            <Label className="text-gray-700 font-medium cursor-pointer">
              Change email address
            </Label>
            <ChevronRight size={20} className="text-gray-400" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
}