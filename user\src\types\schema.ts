import { CryptoCurrency, LoanHealth, SavingsCryptoCurrency, OnboardingSlide } from './enums';

// Props types (data passed to components)
export interface LoanData {
  id: string;
  collateralAmount: number;
  collateralCurrency: CryptoCurrency;
  collateralValueUSD: number;
  currentRate: number;
  marginCall: number;
  ratePair: string;
  health: LoanHealth;
  isHighlighted: boolean;
}

export interface InterestDataPoint {
  month: string;
  value: number;
}

export interface HoldingData {
  currency: CryptoCurrency;
  name: string;
  value: number;
  change: number;
  isPositive: boolean;
}

export interface PortfolioInsights {
  totalValue: number;
  interestEarnedData: InterestDataPoint[];
  holdings: HoldingData[];
}

export interface LoanPageProps {
  loans: LoanData[];
  portfolioInsights: PortfolioInsights;
}

// Savings-related interfaces
export interface OnboardingSlideData {
  id: OnboardingSlide;
  title: string;
  description: string;
  imageUrl: string;
  buttonText: string;
}

export interface SavingsHolding {
  currency: SavingsCryptoCurrency;
  name: string;
  savedAmount: number;
  symbol: string;
}

export interface SavingsData {
  totalSavings: number;
  totalReward: number;
  holdings: SavingsHolding[];
}

export interface TopTrendItem {
  rank: number;
  asset: SavingsCryptoCurrency;
  collateralValue: number;
  volume24h: number;
}

export interface SavingsPageProps {
  onboardingSlides: OnboardingSlideData[];
  savingsData: SavingsData;
  topTrends: TopTrendItem[];
}