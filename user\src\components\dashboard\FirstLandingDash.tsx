"use client";

import WelcomeHeader from "./WelcomeHeader";
import LTV<PERSON>hart from "./LTVChart";
import FeeBreakdown from "./FeeBreakdown";
import QuickstartActions from "./QuickstartActions";
import { LTVChartDataPoint } from "@/data/dashboard-data";

interface FirstLandingDashProps {
  ltvChartData?: LTVChartDataPoint[];
}

export default function FirstLandingDash({ 
  ltvChartData = [] 
}: FirstLandingDashProps) {
  return (
    <div className="w-full flex flex-col p-4 md:p-6">
      <WelcomeHeader />
      <LTVChart data={ltvChartData} />
      <FeeBreakdown />
      <QuickstartActions />
    </div>
  );
}