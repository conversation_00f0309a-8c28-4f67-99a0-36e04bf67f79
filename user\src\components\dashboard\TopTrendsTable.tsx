"use client";

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { TopTrendAsset } from "@/data/dashboard-data";
import CryptoIcon from "../general/CryptoIcon";

interface TopTrendsTableProps {
  trends: TopTrendAsset[];
}

export default function TopTrendsTable({ trends }: TopTrendsTableProps) {
  return (
    <div className="mb-6 w-full">
      <h3 className="text-lg font-semibold mb-4">Top trends</h3>
      <Table className="w-full">
        <TableHeader>
          <TableRow>
            <TableHead className="text-xs text-muted-foreground">Rank</TableHead>
            <TableHead className="text-xs text-muted-foreground">Assets</TableHead>
            <TableHead className="text-xs text-muted-foreground">Collateral value</TableHead>
            <TableHead className="text-xs text-muted-foreground">24h Volume</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {trends.map((trend) => (
            <TableRow key={trend.rank} className="border-b border-border/50">
              <TableCell className="font-medium">#{trend.rank}</TableCell>
              <TableCell>
                <div className="flex items-center gap-3">
                  <CryptoIcon 
                    symbol={trend.symbol}
                    size={24}
                    className="p-1"
                  />
                  <div>
                    <div className="font-medium">{trend.asset}</div>
                    <div className="text-xs text-muted-foreground">{trend.symbol}</div>
                  </div>
                </div>
              </TableCell>
              <TableCell className="font-medium">{trend.collateralValue}</TableCell>
              <TableCell className="font-medium">{trend.volume24h}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}