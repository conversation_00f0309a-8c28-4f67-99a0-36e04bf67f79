"use client";

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { OnboardingSlideData } from '../../types/schema';

interface OnboardingSlideProps {
  slide: OnboardingSlideData;
  onNext: () => void;
  slides: OnboardingSlideData[];
  currentSlide: number;
}

export const OnboardingSlide: React.FC<OnboardingSlideProps> = ({ slide, onNext, slides, currentSlide }) => {
  return (
    <div className="w-full max-w-4xl mx-auto p-6">
      <Card className="p-8 bg-white rounded-3xl shadow-sm">
        <div className="space-y-6">
          <div className="w-full h-64 rounded-2xl overflow-hidden">
            <img
              src={slide.imageUrl}
              alt={`${slide.title} - Thought Catalog on Unsplash`}
              className="w-full h-full object-cover"
              style={{ width: '100%', height: '256px' }}
            />
          </div>

          <div className="space-y-4">
            <h2 className="text-2xl font-semibold text-blue-600">
              {slide.title}
            </h2>

            <p className="text-gray-700 leading-relaxed">
              {slide.description.split('earn extra digital money').map((part, index) => {
                if (index === 0) return part;
                return (
                  <span key={index}>
                    <strong>earn extra digital money</strong>
                    {part}
                  </span>
                );
              })}
            </p>
          </div>

          <div className="flex justify-end pt-4">
            {/* Navigation dots moved from SavingsOnboarding */}
            <div className='flex flex-1'>
              <div className="flex gap-2 pt-2.5">
                {slides.map((_, index) => (
                  <div
                    key={index}
                    className={`w-3 h-3 rounded-full transition-colors ${index === currentSlide ? 'bg-blue-600' : 'bg-gray-300'}`}
                  />
                ))}
              </div>
            </div>
            <Button
              onClick={onNext}
              className="bg-blue-600 hover:bg-blue-700 px-8 py-2 rounded-full"
            >
              {slide.buttonText}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};