"use client";

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { ChevronRight } from 'lucide-react';
import { TopTrendItem } from './TopTrendItem';
import { Separator } from '@/components/ui/separator';
import { TopTrendItem as TopTrendItemType } from '../../types/schema';

interface TopTrendsSidebarProps {
  topTrends: TopTrendItemType[];
}

export const TopTrendsSidebar: React.FC<TopTrendsSidebarProps> = ({ topTrends }) => {
  return (
    <div className="h-full flex flex-col">
      <Card className="p-6 h-full flex flex-col">
        <h3 className="text-lg font-semibold mb-4">Top trends</h3>
        
        <div className="overflow-y-auto mb-4">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-gray-200">
                  <th className="text-left py-2 px-2 text-sm font-medium text-muted-foreground">Rank</th>
                  <th className="text-left py-2 px-2 text-sm font-medium text-muted-foreground">Assets</th>
                  <th className="text-left py-2 px-2 text-sm font-medium text-muted-foreground">Collateral value</th>
                  <th className="text-left py-2 px-2 text-sm font-medium text-muted-foreground">24h Volume</th>
                </tr>
              </thead>
              <tbody>
                {topTrends.map((item, index) => (
                  <TopTrendItem key={index} item={item} />
                ))}
              </tbody>
            </table>
          </div>
        </div>
        <Separator className="my-4" />
        <div className="space-y-3">
          <Button 
            variant="outline" 
            className="w-full justify-between text-blue-600 border-blue-200 hover:bg-blue-50 h-12 rounded-full"
          >
            Demo savings
            <ChevronRight className="w-4 h-4" />
          </Button>
          
          <Button className="w-full bg-blue-600 hover:bg-blue-700 justify-between h-12 rounded-full">
            Start real saving
            <ChevronRight className="w-4 h-4" />
          </Button>
        </div>
      </Card>
    </div>
  );
};
