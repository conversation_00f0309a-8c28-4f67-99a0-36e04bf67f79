import { NextRequest, NextResponse } from 'next/server';
import { fetchDashboardData } from '@/data/dashboard-data';

export async function GET(request: NextRequest) {
  try {
    // Get user ID from query params or headers (in a real app, this would come from auth)
    const { searchParams } = new URL(request.url);
    const userId = searchParams.get('userId') || undefined;

    // Fetch dashboard data
    const dashboardData = await fetchDashboardData(userId);

    return NextResponse.json({
      success: true,
      data: dashboardData,
    });
  } catch (error) {
    console.error('Dashboard API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch dashboard data',
      },
      { status: 500 }
    );
  }
}
