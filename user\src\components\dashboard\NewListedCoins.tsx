"use client";

import { NewCoinAsset } from "@/data/dashboard-data";
import CryptoIcon from "../general/CryptoIcon";

interface NewListedCoinsProps {
  coins: NewCoinAsset[];
}

export default function NewListedCoins({ coins }: NewListedCoinsProps) {
  return (
    <div className="w-full">
      <h3 className="text-lg font-semibold mb-3">New listed coins</h3>
      <p className="text-sm text-muted-foreground mb-4">
        The capabilities of LendBloc Wallet have been enhanced to accommodate the storage and exchange of newly supported assets.
      </p>
      <div className="grid grid-cols-2 gap-3">
        {coins.map((coin) => (
          <div key={coin.symbol} className="flex items-center gap-3 p-3 rounded-lg border border-border">
            <CryptoIcon 
              symbol={coin.symbol}
              size={20}
              className="w-8 h-8"
            />
            <div>
              <div className="font-medium text-sm">{coin.name}</div>
              <div className="text-xs text-muted-foreground">({coin.symbol})</div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}