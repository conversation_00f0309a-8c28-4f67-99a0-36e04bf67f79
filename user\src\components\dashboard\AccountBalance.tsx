"use client";

import { ArrowRight, Info } from "lucide-react";
import { Card, CardContent } from "@/components/ui/card";

interface BalanceItemProps {
  label: string;
  value: string;
  hasInfo?: boolean;
  hasArrow?: boolean;
}

function BalanceItem({ label, value, hasInfo = false, hasArrow = false }: BalanceItemProps) {
  return (
    <Card className="w-full rounded-3xl p-4 bg-[#F8F8F8] group hover:cursor-pointer">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">{label}</span>
          {hasInfo && <Info className="h-4 w-4 text-muted-foreground" />}
        </div>
        <div className="flex items-center gap-2">
          <span className="font-semibold text-foreground group-hover:text-[#466DFF] transition-colors">{value}</span>
          {hasArrow && <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-[#466DFF] transition-colors" />}
        </div>
      </div>
    </Card>
  );
}

export default function AccountBalance() {
  return (
    <div className="space-y-2 mb-6">
      <BalanceItem 
        label="USDT Balance" 
        value="21,558.00" 
        hasArrow={true}
      />
      <BalanceItem 
        label="Savings" 
        value="$4,253.50" 
        hasArrow={true}
      />
      <BalanceItem 
        label="Collateral balance" 
        value="$45,669.12" 
        hasInfo={true}
      />
      <BalanceItem 
        label="Assets value" 
        value="$7,899.45" 
        hasInfo={true}
      />
    </div>
  );
}