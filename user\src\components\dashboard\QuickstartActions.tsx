"use client";

import { <PERSON><PERSON><PERSON> } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

export default function QuickstartActions() {
  return (
    <div>
      <h3 className="text-xl font-semibold text-blue-600 mb-6">Your quickstart</h3>
            <div className="space-y-3">
        <Card className="w-full rounded-3xl hover:rounded-3xl p-0 bg-[#F8F8F8] hover:bg-muted/50 transition-colors cursor-pointer">
          <Button 
            variant="ghost" 
            className="group flex items-center justify-between w-full text-left p-4 h-auto"
          >
            <span className="text-foreground font-medium group-hover:text-[#466DFF]">Purchase crypto</span>
            <ArrowRight className="h-5 w-5 text-[#466DFF]" />
          </Button>
        </Card>
        <Card className="w-full rounded-3xl hover:rounded-3xl p-0 bg-[#F8F8F8] hover:bg-muted/50 transition-colors cursor-pointer">
          <Button 
            variant="ghost" 
            className="group flex items-center justify-between w-full text-left p-4 h-auto"
          >
            <span className="text-foreground font-medium group-hover:text-[#466DFF]">Start receiving crypto</span>
            <ArrowRight className="h-5 w-5 text-[#466DFF]" />
          </Button>
        </Card>
      </div>
    </div>
  );
}