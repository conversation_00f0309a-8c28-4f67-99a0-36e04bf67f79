// Cryptocurrency types
export enum CryptoCurrency {
  BTC = 'BTC',
  ETH = 'ETH',
  USDT = 'USDT',
  ADA = 'ADA',
  DASH = 'DASH',
}

// Health status for loans
export enum LoanHealth {
  GREEN = 'Green',
  YELLOW = 'Yellow',
  RED = 'Red'
}

// Time period for charts
export enum TimePeriod {
  ALL_TIME = 'All time',
  YEARLY = 'Yearly',
  SIX_MONTHS = '6 Months'
}

// Cryptocurrency types for savings
export enum SavingsCryptoCurrency {
  BTC = 'Bitcoin',
  ETH = 'Ethereum',
  BNB = 'BNB',
  SOL = 'Solana',
  DASH = 'DASH',
  POLKADOT = 'Polkadot',
  MONERO = 'Monero'
}

// Onboarding slide types
export enum OnboardingSlide {
  INTRO = 'intro',
  BENEFITS = 'benefits',
  POTENTIAL = 'potential'
}