"use client";

import React from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChevronRight } from 'lucide-react';
import { SavingsHolding } from './SavingsHolding';
import { TopTrendItem } from './TopTrendItem';
import { SavingsData, TopTrendItem as TopTrendItemType } from '../../types/schema';
import { formatSavingsAmount } from '../../utils/formatters';
import { Separator } from "@/components/ui/separator"

interface SavingsDashboardProps {
  savingsData: SavingsData;
  topTrends: TopTrendItemType[];
}

export const SavingsDashboard: React.FC<SavingsDashboardProps> = ({ savingsData, topTrends }) => {
  return (
    <div className="w-full p-6 ">
        {/* Main Content */}
        <div className="w-full flex flex-col gap-6">
          {/* Savings Summary */}
          <div className="flex items-center justify-between">
            <div>
              <div className="text-sm text-muted-foreground mb-1">Total savings</div>
              <div className="text-4xl font-bold text-blue-600">
                {formatSavingsAmount(savingsData.totalSavings)}
              </div>
            </div>
            <Badge className="bg-blue-600 text-white px-4 py-2 text-sm">
              Total reward: {formatSavingsAmount(savingsData.totalReward)}
            </Badge>
          </div>
          
          {/* Holdings List */}
          <div className="space-y-4 flex-1 overflow-y-auto">
            {savingsData.holdings.map((holding, index) => (
              <SavingsHolding key={index} holding={holding} />
            ))}
          </div>
        </div>
    </div>
  );
};