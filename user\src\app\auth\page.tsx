"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAppContext } from "@/hooks/context/useAppContext";
import { useViewportType } from "@/hooks/use-viewport";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";

/**
 * Simple Auth page implementing login/logout using the app context.
 * - If already authenticated, redirect to home (/).
 * - Provides a "Dev Quick Login" that uses the existing devQuickLogin action.
 * - Provides a "Logout" when already authenticated.
 */
export default function AuthPage() {
  const app = useAppContext();
  const router = useRouter();
  const { isMobile } = useViewportType();

  // If user is already authenticated, send them away from /auth
  useEffect(() => {
    if (app.isAuthenticated) {
      router.replace("/");
    }
  }, [app.isAuthenticated, router]);

  if (isMobile) {
    return (
      <div className="w-full h-screen p-4 bg-gray-300/50 overflow-hidden">
        <div className="h-full flex items-center justify-center">
          <Card className="w-full max-w-sm p-4 md:p-6 space-y-4">
            <h1 className="text-xl font-semibold">Authentication</h1>

            {!app.isAuthenticated ? (
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">You are not logged in.</p>
                <Button
                  className="w-full"
                  onClick={() => {
                    app.setDevMode(true);
                    app.devQuickLogin();
                    // After login, route home
                    router.replace("/");
                  }}
                >
                  Dev Quick Login
                </Button>
              </div>
            ) : (
              <div className="space-y-3">
                <p className="text-sm">You are logged in{app.user ? ` as ${app.user.name}` : ""}.</p>
                <Button
                  variant="secondary"
                  className="w-full"
                  onClick={() => {
                    app.devQuickLogout();
                    // After logout, remain on /auth (guard will also keep you here)
                    router.replace("/auth");
                  }}
                >
                  Logout
                </Button>
              </div>
            )}
          </Card>
        </div>
      </div>
    )
  };

  return (
    <div className="w-full h-screen p-4 bg-gray-300/50 overflow-hidden">
      <div className="h-full flex items-center justify-center">
        <Card className="w-full max-w-sm p-4 md:p-6 space-y-4">
          <h1 className="text-xl font-semibold">Authentication</h1>

          {!app.isAuthenticated ? (
            <div className="space-y-3">
              <p className="text-sm text-muted-foreground">You are not logged in.</p>
              <Button
                className="w-full"
                onClick={() => {
                  app.setDevMode(true);
                  app.devQuickLogin();
                  // After login, route home
                  router.replace("/");
                }}
              >
                Dev Quick Login
              </Button>
            </div>
          ) : (
            <div className="space-y-3">
              <p className="text-sm">You are logged in{app.user ? ` as ${app.user.name}` : ""}.</p>
              <Button
                variant="secondary"
                className="w-full"
                onClick={() => {
                  app.devQuickLogout();
                  // After logout, remain on /auth (guard will also keep you here)
                  router.replace("/auth");
                }}
              >
                Logout
              </Button>
            </div>
          )}
        </Card>
      </div>
    </div>
  );
}