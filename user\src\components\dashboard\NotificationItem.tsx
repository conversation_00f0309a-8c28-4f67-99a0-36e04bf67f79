"use client";

interface NotificationItemProps {
  time: string;
  action: string;
  amount: string;
  hasRedDot?: boolean;
}

export default function NotificationItem({ time, action, amount, hasRedDot = false }: NotificationItemProps) {
  return (
    <div className="flex items-center justify-between py-2 px-1">
      <div className="flex items-center gap-3">
        <span className="text-xs text-muted-foreground font-medium">{time}</span>
        <span className="text-sm text-foreground">{action}</span>
        {hasRedDot && (
          <div className="w-2 h-2 rounded-full bg-red-500"></div>
        )}
      </div>
      <span className="text-sm font-medium text-foreground">{amount}</span>
    </div>
  );
}