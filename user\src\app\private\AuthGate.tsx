"use client";

import { PropsWithChildren, useEffect, useMemo, useRef, useState } from "react";
import { usePathname, useRouter } from "next/navigation";
import { useAppContext } from "@/hooks/context/useAppContext";
import ContextDemo from "@/hooks/context/contextDemo";

/**
 * Client-only auth gate to avoid hydration mismatches and avoid blank screens.
 * Key changes:
 * - Use router.replace only when the destination differs to avoid infinite loops.
 * - Do not render-null on same-route transitions; instead show a minimal shell to keep the page responsive.
 * - The decision waits one rAF for persisted state hydration.
 */
export function AuthGate({ children }: PropsWithChildren) {
  const { isAuthenticated } = useAppContext();
  const router = useRouter();
  const pathname = usePathname();

  const isAuthRoute = useMemo(() => pathname?.startsWith("/auth") ?? false, [pathname]);

  const [hydrated, setHydrated] = useState(false);
  const [redirectingTo, setRedirectingTo] = useState<string | null>(null);
  const redirectingRef = useRef(false);

  // Ensure Zustand persisted state is hydrated before making decisions
  useEffect(() => {
    const id = requestAnimationFrame(() => setHydrated(true));
    return () => cancelAnimationFrame(id);
  }, []);

  useEffect(() => {
    if (!hydrated) return;

    // Compute desired destination based on current state and route
    let target: string | null = null;
    if (!isAuthenticated && !isAuthRoute) {
      target = "/auth";
    } else if (isAuthenticated && isAuthRoute) {
      target = "/";
    }

    // If no redirect needed, clear any redirect flags and show content
    if (!target) {
      redirectingRef.current = false;
      setRedirectingTo(null);
      return;
    }

    // If already on target, don't attempt replace; just clear redirecting and render
    if (pathname === target) {
      redirectingRef.current = false;
      setRedirectingTo(null);
      return;
    }

    // Initiate redirect once
    if (!redirectingRef.current) {
      redirectingRef.current = true;
      setRedirectingTo(target);
      router.replace(target);
    }
  }, [hydrated, isAuthenticated, isAuthRoute, pathname, router]);

  // While hydrating or navigating, render a minimal stable shell to avoid blanks
  if (!hydrated || redirectingTo) {
    return (
      <>
        <div style={{ position: "fixed", right: 12, bottom: 12, zIndex: 50, maxWidth: 420 }}>
          <ContextDemo />
        </div>
        <div style={{ minHeight: "100vh" }} />
      </>
    );
  }

  return (
    <>
      <div style={{ position: "fixed", right: 12, bottom: 12, zIndex: 50, maxWidth: 420 }}>
        <ContextDemo />
      </div>
      {children}
    </>
  );
}
