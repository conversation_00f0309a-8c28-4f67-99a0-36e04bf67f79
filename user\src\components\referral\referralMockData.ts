// Mock data for referral dashboard

// Currency and percentage formatting functions
export const formatCurrency = (amount: number): string => {
  return `$${amount.toFixed(2)} USDT`;
};

export const formatPercentage = (value: number): string => {
  return `${value.toFixed(1)}%`;
};

export const formatReferralLink = (code: string): string => {
  return `https://lendbloc.io/?r=${code}`;
};

// Referral performance metrics data
export const mockReferralPerformanceData = [
  { month: "Jan", totalReferrals: 20, activeLoans: 15 },
  { month: "Feb", totalReferrals: 35, activeLoans: 25 },
  { month: "Mar", totalReferrals: 45, activeLoans: 30 },
  { month: "Apr", totalReferrals: 25, activeLoans: 20 },
  { month: "May", totalReferrals: 55, activeLoans: 40 },
  { month: "Jun", totalReferrals: 35, activeLoans: 25 },
  { month: "Jul", totalReferrals: 65, activeLoans: 50 },
  { month: "Aug", totalReferrals: 45, activeLoans: 35 },
  { month: "Sep", totalReferrals: 75, activeLoans: 55 },
  { month: "Oct", totalReferrals: 55, activeLoans: 40 },
  { month: "Nov", totalReferrals: 85, activeLoans: 65 },
  { month: "Dec", totalReferrals: 65, activeLoans: 50 },
];

// Referral earning metrics data
export const mockReferralEarningData = [
  { month: "Jan", earnings: 150 },
  { month: "Feb", earnings: 180 },
  { month: "Mar", earnings: 220 },
  { month: "Apr", earnings: 190 },
  { month: "May", earnings: 250 },
  { month: "Jun", earnings: 210 },
  { month: "Jul", earnings: 280 },
  { month: "Aug", earnings: 240 },
  { month: "Sep", earnings: 320 },
  { month: "Oct", earnings: 290 },
  { month: "Nov", earnings: 350 },
  { month: "Dec", earnings: 310 },
];

// Portfolio insights data
export const mockPortfolioData = [
  { month: "Jan", value: 100 },
  { month: "Feb", value: 120 },
  { month: "Mar", value: 110 },
  { month: "Apr", value: 140 },
  { month: "May", value: 160 },
  { month: "Jun", value: 150 },
  { month: "Jul", value: 180 },
  { month: "Aug", value: 200 },
];

// Root props data
export const mockRootProps = {
  totalEarnings: 841.25,
  referralCode: "redCarpetCB" as const,
  currentPeriod: "All time" as const,
};

// Chart configuration
export const chartConfig = {
  totalReferrals: {
    label: "Total referrals",
    color: "#22c55e",
  },
  activeLoans: {
    label: "Active loans",
    color: "#3b82f6",
  },
  earnings: {
    label: "Earnings",
    color: "#3b82f6",
  },
  value: {
    label: "Portfolio Value",
    color: "#22c55e",
  },
};