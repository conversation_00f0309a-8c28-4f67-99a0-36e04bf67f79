"use client";

import { Search } from "lucide-react";
import { Input } from "@/components/ui/input";

export default function TransactionSearch() {
  return (
    <div className="relative flex-1">
      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
      <Input 
        placeholder="Search by ID" 
        className="pl-10 bg-muted/30 border-border h-12 rounded-3xl"
      />
    </div>
  );
}