"use client";

import { Card } from "@/components/ui/card";

interface ReferralEarningsSummaryProps {
  totalEarnings: number;
}

export function ReferralEarningsSummary({ totalEarnings }: ReferralEarningsSummaryProps) {
  return (
    <Card className="p-6 shadow-sm rounded-4xl">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">Referral earnings</h3>
        <div className="flex items-center gap-2">
          <div className="text-2xl font-bold text-blue-600">
            ${totalEarnings.toFixed(2)} USDT
          </div>
          <svg className="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
      </div>
    </Card>
  );
}