import { fetchDashboardData } from "@/data/dashboard-data";
import MainDash from "./MainDash";
import FirstLandingDash from "./FirstLandingDash";
import { TopTrendsTable } from "./TopTrendsTable";
import { NewListedCoins } from "./NewListedCoins";
import AccountTabContent from "./AccountTabContent";
import TransactionsTabContent from "./TransactionsTabContent";

interface DashboardWrapperProps {
  userId?: string;
}

export default async function DashboardWrapper({ userId }: DashboardWrapperProps) {
  // Fetch dashboard data server-side
  const dashboardData = await fetchDashboardData(userId);

  return {
    hasUserData: dashboardData.hasUserData,
    mainDashData: {
      interestData: dashboardData.interestData,
      loanData: dashboardData.loanData,
    },
    firstLandingData: {
      ltvChartData: dashboardData.ltvChartData,
    },
    sidebarData: {
      topTrendsData: dashboardData.topTrendsData,
      newCoinsData: dashboardData.newCoinsData,
    },
  };
}
