"use client";

import React from 'react';
import { <PERSON><PERSON><PERSON>, <PERSON>, XAxi<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid } from 'recharts';
import { ChartContainer, ChartTooltip, ChartTooltipContent } from '@/components/ui/chart';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { InterestDataPoint } from '../../types/schema';
import { TimePeriod } from '../../types/enums';

interface PortfolioChartProps {
  data: InterestDataPoint[];
  selectedPeriod: string;
  onPeriodChange: (period: string) => void;
}

const chartConfig = {
  value: {
    label: "Interest Earned",
    color: "#10b981",
  },
};

export const PortfolioChart: React.FC<PortfolioChartProps> = ({
  data,
  selectedPeriod,
  onPeriodChange,
}) => {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Interest earned all time</h3>
        <Select value={selectedPeriod} onValueChange={onPeriodChange}>
          <SelectTrigger className="w-32 text-sm">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value={TimePeriod.ALL_TIME}>All time</SelectItem>
            <SelectItem value={TimePeriod.YEARLY}>Yearly</SelectItem>
            <SelectItem value={TimePeriod.SIX_MONTHS}>6 Months</SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      <div className="w-full rounded-xl border border-none bg-muted/20 p-2 pl-0">
        <div className="h-[300px] w-full rounded-lg bg-background overflow-hidden">
          <ChartContainer config={chartConfig} className="h-full w-full">
            <LineChart data={data} margin={{ top: 10, right: 10, left: 0, bottom: 0 }}>
              <CartesianGrid vertical={false} stroke="#e5e7eb" />
              <XAxis
                dataKey="month"
                tickLine={false}
                axisLine={{ stroke: '#d1d5db' }}
                tickMargin={8}
                tick={{ fontSize: 12, fill: '#6b7280' }}
              />
              <YAxis
                tickLine={false}
                axisLine={{ stroke: '#d1d5db' }}
                tickFormatter={(val) => `$${val}`}
                width={60}
                domain={[0, 'auto']}
                tick={{ fontSize: 12, fill: '#6b7280' }}
              />
              <ChartTooltip
                cursor={{ stroke: '#34d399', strokeOpacity: 0.15 }}
                content={<ChartTooltipContent />}
              />
              <Line
                type="monotone"
                dataKey="value"
                stroke={chartConfig.value.color}
                strokeWidth={3}
                dot={false}
                activeDot={{ r: 4 }}
              />
            </LineChart>
          </ChartContainer>
        </div>
      </div>
    </div>
  );
};