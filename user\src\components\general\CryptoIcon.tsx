"use client"

import { Bitcoin, Ethereum, Tether, Cardano, Dash, Solana, Monero, PolkadotNew } from "@thirdweb-dev/chain-icons"
import { CryptoCurrency } from "@/types/enums"

interface CryptoIconProps {
  symbol?: string;
  size?: number;
  className?: string;
}

// Extended mapping for more cryptocurrencies beyond the enum
const getCryptoIcon = (symbol: string | undefined) => {
  const normalizedSymbol = symbol ? symbol.toLowerCase() : 'bitcoin';
  
  switch (normalizedSymbol) {
    case 'btc':
    case 'bitcoin':
      return Bitcoin;
    case 'eth':
    case 'ethereum':
      return Ethereum;
    case 'usdt':
    case 'tether':
      return Tether;
    case 'ada':
    case 'cardano':
      return Cardano;
    case 'dash':
      return Dash;
    // For cryptocurrencies not available in thirdweb icons, we'll use Bitcoin as fallback
    case 'sol':
      return Solana;
    case 'solana':
      return Solana;
    case 'xmr':
      return Monero;
    case 'monero':
      return Monero;
    case 'dot':
      return PolkadotNew
    case 'polkadot':
      return PolkadotNew;
    default:
      return Bitcoin;
  }
};

const getCryptoIconColor = (symbol: string | undefined) => {
  const normalizedSymbol = symbol ? symbol.toLowerCase() : 'bitcoin';
  
  switch (normalizedSymbol) {
    case 'btc':
    case 'bitcoin':
      return "#F7931A"; // Bitcoin orange
    case 'eth':
    case 'ethereum':
      return "#DCDCDC"; // Ethereum blue
    case 'usdt':
    case 'tether':
      return "#26A17B"; // Tether green
    case 'ada':
      return "#FFFFFF"; // Cardano blue
    case 'cardano':
      return "#FFFFFF"; // Cardano blue
    case 'dash':
      return "#FFFFFF"; // Dash blue
    case 'sol':
      return "#0b0314"; // Solana purple
    case 'solana':
      return "#0b0314"; // Solana purple
    case 'xmr':
    case 'monero':
      return "#FF6600"; // Monero orange
    case 'dot':
    case 'polkadot':
      return "#FFFFFF"; // Polkadot pink
    default:
      return "#F7931A"; // Default to Bitcoin orange
  }
};

export default function CryptoIcon({ symbol, size = 24, className = "" }: CryptoIconProps) {
  const IconComponent = getCryptoIcon(symbol);
  const iconColor = getCryptoIconColor(symbol);

  return (
    <div
      className={`rounded-full flex items-center justify-center ${className}`}
      style={{ backgroundColor: iconColor }}
    >
      <IconComponent
        width={size}
        height={size}
        fill="white"
      />
    </div>
  );
}

// Export the utility functions for use in other components
export { getCryptoIcon, getCryptoIconColor };