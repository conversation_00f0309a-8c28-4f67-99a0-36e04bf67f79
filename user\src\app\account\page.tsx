"use client";

import { useState } from "react";
import { useViewportType } from "@/hooks/use-viewport";
import { ProfileHeader } from "@/components/account/ProfileHeader";
import { PrivacySettingsCard } from "@/components/account/PrivacySettingsCard";
import { AdditionalSettingsCard } from "@/components/account/AdditionalSettingsCard";
import { mockRootProps } from "@/app/accountMockData";
import { UserProfile, PrivacySettings, AppSettings } from "@/types/accountSchema";

export default function AccountPage() {
  const { isMobile, isTablet } = useViewportType();
  const [user, setUser] = useState<UserProfile>(mockRootProps.user);
  const [privacySettings, setPrivacySettings] = useState<PrivacySettings>(mockRootProps.privacySettings);
  const [appSettings, setAppSettings] = useState<AppSettings>(mockRootProps.appSettings);

  const handleNicknameChange = (nickname: string) => {
    setUser(prev => ({ ...prev, nickname }));
  };

  const handlePrivacySettingsChange = (newSettings: Partial<PrivacySettings>) => {
    setPrivacySettings(prev => ({ ...prev, ...newSettings }));
  };

  const handleAppSettingsChange = (newSettings: Partial<AppSettings>) => {
    setAppSettings(prev => ({ ...prev, ...newSettings }));
  };

  // Mobile and Tablet Layout (< 1200px)
  if (isMobile || isTablet) {
    return (
      <div className="w-full h-screen p-2 sm:p-4 bg-gray-50 overflow-hidden">
        <div className="w-full h-full flex flex-col gap-2 sm:gap-4 overflow-y-auto">
          <div className="flex-shrink-0">
            <ProfileHeader
              user={user}
              onNicknameChange={handleNicknameChange}
            />
          </div>
          <div className="flex-shrink-0">
            <PrivacySettingsCard
              settings={privacySettings}
              onSettingsChange={handlePrivacySettingsChange}
            />
          </div>
          <div className="flex-shrink-0">
            <AdditionalSettingsCard
              appSettings={appSettings}
              kycStatus={mockRootProps.kycStatus}
              onAppSettingsChange={handleAppSettingsChange}
            />
          </div>
        </div>
      </div>
    );
  }

  // Desktop Layout (≥ 1200px)
  return (
    <div className="w-full h-screen p-4 lg:p-6 bg-gray-300/50 overflow-hidden">
      <div className="w-full h-full grid grid-cols-1 gap-4 lg:gap-6 xl:grid-cols-[6fr_4fr] overflow-hidden">
        <div className="min-h-0 max-h-full flex flex-col gap-4 lg:gap-6 overflow-hidden">
          {/* Left column - Profile and Privacy Settings */}
          <div className="flex-shrink-0">
            <ProfileHeader
              user={user}
              onNicknameChange={handleNicknameChange}
            />
          </div>
          <div className="flex-shrink-0">
            <PrivacySettingsCard
              settings={privacySettings}
              onSettingsChange={handlePrivacySettingsChange}
            />
          </div>
        </div>

        {/* Right column - Additional Settings */}
        <div className="min-h-0 max-h-full overflow-hidden">
          <AdditionalSettingsCard
            appSettings={appSettings}
            kycStatus={mockRootProps.kycStatus}
            onAppSettingsChange={handleAppSettingsChange}
          />
        </div>
      </div>
    </div>
  );
}